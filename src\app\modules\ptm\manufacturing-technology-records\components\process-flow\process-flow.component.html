<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[10px]">
        <label class="tw-font-semibold tw-text-base label">Chi tiết Process Flow</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Track Changes" class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"></button>
            <button
                (click)="exportProcessFlow()"
                pButton
                type="button"
                label="Xuất excel"
                class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"
            ></button>
            <button pButton type="button" label="Import file" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>
    </div>

    <app-form-item label="" [disableAutoErrorMessage]="true">
        <app-form #form [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
            <div formArrayName="listProcessFlow">
                <p-panel [toggleable]="true">
                    <p-table styleClass="p-datatable-gridlines" [value]="processFlow.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 9rem">Step</th>
                                <th style="min-width: 9rem">OC <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Next OC</th>
                                <th style="min-width: 9rem">Đặc tính công việc (Symbol) <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Operation Description (OD) <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Product and process characteristics <span class="tw-text-red-500">*</span></th>
                                <th style="min-width: 9rem">Online</th>
                                <th style="min-width: 9rem">Offline</th>
                                <th style="max-width: 5rem">Thao tác</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex">
                                <td>
                                    <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>

                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input
                                            type="text"
                                            class="tw-w-full"
                                            pInputText
                                            maxlength="4"
                                            formControlName="oc"
                                            (input)="processFlow.updateValueAndValidity()"
                                        />
                                        <div *ngIf="item.get('oc')?.touched && item.get('oc')?.invalid" class="text-red-400">
                                            <ng-container *ngIf="item.get('oc')?.errors?.['required']; else maxLengthError">
                                                Trường này là bắt buộc
                                            </ng-container>

                                            <ng-template #maxLengthError>
                                                <ng-container *ngIf="item.get('oc')?.errors?.['maxlength']; else patternError">
                                                    Trường này không được phép quá 4 ký tự
                                                </ng-container>
                                            </ng-template>

                                            <ng-template #patternError>
                                                <ng-container *ngIf="item.get('oc')?.errors?.['pattern']; else duplicateError">
                                                    Giá trị không hợp lệ
                                                </ng-container>
                                            </ng-template>

                                            <ng-template #duplicateError>
                                                <ng-container *ngIf="item.get('oc')?.errors?.['duplicate']"> OC đã tồn tại </ng-container>
                                            </ng-template>
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input type="text" class="tw-w-full" pInputText maxlength="1000" formControlName="nextOc" />
                                        <div *ngIf="item.get('nextOc')?.touched && item.get('nextOc')?.errors?.['invalidNextOc']" class="text-red-400">
                                            Giá trị không hợp lệ
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-dropdown
                                            [options]="symbolType"
                                            formControlName="symbol"
                                            optionLabel="label"
                                            optionValue="value"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                        />
                                        <div
                                            *ngIf="item.get('symbol')?.invalid && item.get('symbol')?.touched && item.get('symbol')?.errors?.required"
                                            class="text-red-400"
                                        >
                                            Trường này là bắt buộc
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <input type="text" class="tw-w-full" pInputText maxlength="100" formControlName="operationDescription" />
                                        <div
                                            *ngIf="
                                                item.get('operationDescription')?.invalid &&
                                                item.get('operationDescription')?.touched &&
                                                item.get('operationDescription')?.errors?.required
                                            "
                                            class="text-red-400"
                                        >
                                            Trường này là bắt buộc
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-dropdown
                                            [options]="productProcessType"
                                            formControlName="productAndProcessCharacteristics"
                                            optionLabel="label"
                                            optionValue="value"
                                            class="tw-w-full"
                                            [showClear]="true"
                                            appendTo="body"
                                        />
                                        <div
                                            *ngIf="
                                                item.get('productAndProcessCharacteristics')?.invalid &&
                                                item.get('productAndProcessCharacteristics')?.touched &&
                                                item.get('productAndProcessCharacteristics')?.errors?.required
                                            "
                                            class="text-red-400"
                                        >
                                            Trường này là bắt buộc
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-checkbox
                                            name="online"
                                            formControlName="online"
                                            [binary]="true"
                                            (onChange)="item.updateValueAndValidity()"
                                        ></p-checkbox>
                                        <div
                                            *ngIf="
                                                !item.get('online')?.value &&
                                                !item.get('offline')?.value &&
                                                (item.get('online')?.dirty || item.get('online')?.touched)
                                            "
                                            class="text-red-400"
                                        >
                                            <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                        </div>
                                        <div
                                            *ngIf="
                                                item.get('online')?.value &&
                                                item.get('offline')?.value &&
                                                (item.get('online')?.dirty || item.get('online')?.touched)
                                            "
                                            class="text-red-400"
                                        >
                                            <span>Không thể chọn cả hai trường: Online và Offline</span>
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <app-form-item label="" [disableAutoErrorMessage]="true">
                                        <p-checkbox
                                            name="offline"
                                            formControlName="offline"
                                            [binary]="true"
                                            (onChange)="item.updateValueAndValidity()"
                                        ></p-checkbox>
                                        <div
                                            *ngIf="
                                                !item.get('online')?.value &&
                                                !item.get('offline')?.value &&
                                                (item.get('offline')?.dirty || item.get('offline')?.touched)
                                            "
                                            class="text-red-400"
                                        >
                                            <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                        </div>
                                        <div
                                            *ngIf="
                                                item.get('online')?.value &&
                                                item.get('offline')?.value &&
                                                (item.get('online')?.dirty || item.get('online')?.touched)
                                            "
                                            class="text-red-400"
                                        >
                                            <span>Không thể chọn cả hai trường: Online và Offline</span>
                                        </div>
                                    </app-form-item>
                                </td>
                                <td>
                                    <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                        <button
                                            class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                            pTooltip="Hủy"
                                            tooltipPosition="top"
                                            type="button"
                                            (click)="removeItem(rowIndex)"
                                        >
                                            <span class="pi pi-times"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                    <div class="tw-mt-3">
                        <p-button label="Thêm dòng" icon="pi pi-plus" severity="info" size="small" (click)="addItem()"></p-button>
                    </div>
                </p-panel>
            </div>
            <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
                <!-- <p-button
                    label="Lưu"
                    type="submit"
                    [disabled]="formGroup.invalid || (isSaving | async)"
                    [loading]="isSaving | async"
                    (onClick)="handleSubmit()"
                    loadingIcon="pi pi-spinner pi-spin"
                >
                </p-button> -->
                <p-button label="Lưu" type="submit" [loading]="isSaving | async" (onClick)="handleSubmit($event)" loadingIcon="pi pi-spinner pi-spin">
                </p-button>
                <p-button
                    label="Gửi Preview"
                    type="button"
                    [disabled]="isSubmitting | async"
                    [loading]="isSubmitting | async"
                    loadingIcon="pi pi-spinner pi-spin"
                >
                </p-button>
                <p-button label="Phê duyệt" type="button" [disabled]="isApproving | async" [loading]="isApproving | async" loadingIcon="pi pi-spinner pi-spin">
                </p-button>
            </div>
        </app-form>
    </app-form-item>
</ng-container>
