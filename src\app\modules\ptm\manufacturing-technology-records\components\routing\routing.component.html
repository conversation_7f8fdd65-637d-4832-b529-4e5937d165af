<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>

    <div class="p-fluid tw-space-y-2">
        <div class="tw-flex tw-items-center tw-gap-3">
            <label class="label tw-w-[15rem]" for="pf">Chi tiết Routing</label>

            <button pButton type="button" label="Tạo tự động" class="p-button-sm tw-max-w-[150px]"></button>
            <button pButton type="button" label="Upload file" class="p-button-sm p-button-secondary tw-max-w-[100px]"></button>
        </div>
    </div>

    <div class="p-fluid tw-space-y-2">
        <app-patent-graph [nodes]="nodes" [links]="links"></app-patent-graph>
    </div>

    <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
        <p-button label="Lưu" type="submit" [loading]="isSaving | async" (onClick)="handleSubmit($event)" loadingIcon="pi pi-spinner pi-spin"> </p-button>
        <p-button label="Gửi Preview" type="button" [disabled]="isSubmitting | async" [loading]="isSubmitting | async" loadingIcon="pi pi-spinner pi-spin">
        </p-button>
        <p-button label="Phê duyệt" type="button" [disabled]="isApproving | async" [loading]="isApproving | async" loadingIcon="pi pi-spinner pi-spin">
        </p-button>
    </div>
</ng-container>
