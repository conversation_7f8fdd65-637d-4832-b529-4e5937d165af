<ng-container>
    <div class="tw-my-2">
        <label>Chi tiết tài liệu chất lượ<PERSON></label>
    </div>

    <div #container>
        <app-form #form [formGroup]="formGroup" (onSubmit)="onSave()">
            <div formArrayName="items">
                <p-panel [toggleable]="true">
                    <ng-template pTemplate="icons">
                        <button
                            pButton
                            type="button"
                            class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                            appFullscreenToggle
                            [target]="container"
                        ></button>
                    </ng-template>
                    <p-table styleClass="p-datatable-gridlines" [value]="items.controls">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="min-width: 2rem">STT</th>
                                <th style="min-width: 9rem">Tên tài liệu</th>
                                <th style="min-width: 9rem">Version</th>
                                <th style="min-width: 9rem">Bi<PERSON><PERSON> mẫu</th>
                                <th style="min-width: 9rem">Trạng thái</th>
                                <th style="min-width: 9rem">Last Update</th>
                                <th style="min-width: 9rem">Ng<PERSON>ời cập nhật</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                            <tr [formGroupName]="rowIndex">
                                <!-- STT -->
                                <td>
                                    <app-form-item label=""> {{ rowIndex + 1 }}</app-form-item>
                                </td>
                                <!-- Tên tài liệu -->
                                <td>
                                    <app-form-item label="" validateTrigger="touched">
                                        <input pInputText maxlength="50" type="text" formControlName="name" class="tw-w-full" />
                                    </app-form-item>
                                </td>
                                <!-- Version -->
                                <td>
                                    <app-form-item label="">
                                        <app-combobox-nonRSQL
                                            #versionSelect
                                            [fetchOnInit]="true"
                                            type="select-one"
                                            formControlName="versionSelected"
                                            [fieldValue]="versionConfigs[rowIndex].fieldValue"
                                            [fieldLabel]="versionConfigs[rowIndex].fieldLabel"
                                            [url]="versionConfigs[rowIndex].url"
                                            [param]="versionConfigs[rowIndex].param"
                                            [additionalParams]="{ size: 100 }"
                                            (onChange)="onVersionSelect($event, rowIndex)"
                                        >
                                        </app-combobox-nonRSQL>
                                        <!-- <app-upload-custom
                                        #OtherDocsUploader
                                        [loading]="loadingRows.get(item.get('id')?.value)"
                                        [filePath]="item.get('filePath')?.value"
                                        [limit]="1"
                                        (onChange)="onFileSelected($event, item.get('id')?.value)"
                                        (onClear)="onClearFile(item.get('id')?.value)"
                                    ></app-upload-custom> -->
                                    </app-form-item>
                                </td>
                                <!-- Biểu mẫu -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ items.at(rowIndex).get('formExample')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Trạng thái -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ items.at(rowIndex).get('status')!.value }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Last Update -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ items.at(rowIndex).get('lastUpdate')!.value | date: 'hh:mm dd/MM/yyyy' }}</span>
                                    </app-form-item>
                                </td>
                                <!-- Người cập nhật -->
                                <td>
                                    <app-form-item label="">
                                        <span>{{ items.at(rowIndex).get('updatedBy')!.value }}</span>
                                    </app-form-item>
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </p-panel>
            </div>

            <app-tab-action-buttons [form]="formGroup" [mode]="mode" [status]="version.status" [isSaving]="isBusy$"></app-tab-action-buttons>
        </app-form>
    </div>
</ng-container>
