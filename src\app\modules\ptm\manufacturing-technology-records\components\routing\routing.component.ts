// src/app/shared/components/routing/routing.component.ts
import { Component, On<PERSON>estroy, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { ButtonModule } from 'primeng/button';
import { PatentGraphComponent } from './components/chart/patent-graph.component';
import { Observable } from 'rxjs';
import { Routing } from 'src/app/models/interface/ptm/routing';
import { RoutingService } from 'src/app/services/ptm/routing/routing.service';
import { MessageService } from 'primeng/api';
@Component({
    selector: 'app-routing',
    standalone: true,
    imports: [CommonModule, FormsModule, InputTextareaModule, InfoShareComponent, ButtonModule, PatentGraphComponent],
    templateUrl: './routing.component.html',
    styleUrls: ['./routing.component.scss'],
    providers: [RoutingService],
})
export class RoutingComponent implements OnInit, OnDestroy {
    nodes = [{ id: 'Apple' }, { id: 'Samsung' }, { id: 'Google' }, { id: 'Motorola' }];

    links = [
        { source: 'Apple', target: 'Samsung' },
        { source: 'Apple', target: 'Motorola' },
        { source: 'Google', target: 'Apple' },
    ];
    @Input() title: string = '';
    @Input() isSaving!: Observable<boolean>;
    @Input() isSubmitting!: Observable<boolean>;
    @Input() isApproving!: Observable<boolean>;

    constructor(
        private routingService: RoutingService,
        private messageService: MessageService,
    ) {}

    content = '';
    getData(): string {
        return this.content;
    }
    ngOnInit(): void {
        console.log('✅ [routing] Mounted');
        this.initForm();
    }

    ngOnDestroy(): void {
        console.log('🧹 [routing] Unmounted');
    }

    initForm() {
        this.routingService.getRouting('3').subscribe({
            next: (res) => {},
            error: () => {},
        });
    }

    handleSubmit(value: Routing): void {
        console.log(value);
        // Gọi callback nếu có
        this.routingService.create(value, '0').subscribe({
            next: (res) => {
                this.messageService.add({
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Tạo routing thành công',
                });
            },
            error: () => {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Lỗi',
                    detail: 'Không thể tạo routing',
                });
            },
        });
    }
}
