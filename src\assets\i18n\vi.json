{"error": {"Template": {"nameDuplicate": "Tên mẫu kiểm tra đã tồn tại", "nameIsNull": "Tên mẫu kiểm tra không được bỏ trống", "nameLengthBigger255": "Tên mẫu kiểm tra không quá 255 ký tự", "workTypeIsNull": "<PERSON><PERSON><PERSON> việc không được bỏ trống", "doesNotExist": "Mẫu kiểm tra không tồn tại"}, "Checklist": {"nameIsNull": "Ti<PERSON>u đề Checklist kh<PERSON>ng được bỏ trống", "nameLengthBigger255": "Tiêu đề Checklist không quá 255 ký tự", "descriptionLengthBigger255": "<PERSON><PERSON> tả Checklist không quá 255 ký tự", "numberImageBigger100": "Số lượ<PERSON> hình <PERSON>nh tối đa hai chữ số", "typeIsNull": "Checklist ch<PERSON><PERSON><PERSON><PERSON> gán kiểu dữ liệu", "numberImageIsNull": "Checklist ki<PERSON><PERSON> chưa có số lượng <PERSON>nh", "valueIsNull": "<PERSON><PERSON><PERSON> trị của Checklist (lựa chọn/ yes_no) đang trống", "TemplateIdIsNull": "Mẫu kiểm tra không tồn tại"}, "Contract": {"timeIsIncorrect": "Thời gian dự kiến ngày bắt đầu không được lớn hơn ngày kết thúc", "nameNull": "Tên dự án không được bỏ trống", "nameLengthIs255": "Tên dự án không được dài quá 255 kí tự", "statusIsIncorrect": "Trạng thái dự án không phù hợp", "DuplicateName": "Tên dự án đã tồn tại", "contractNotFound": "Dự án không tồn tại", "invalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại", "IdIsNull": "Id d<PERSON> án không hợp lệ"}, "Task": {"TaskNotFound": "<PERSON><PERSON>ng vi<PERSON><PERSON> không tồn tại", "TaskIsComplete": "<PERSON><PERSON><PERSON> việc đã hoàn thành", "CantApprove": "Ngườ<PERSON> dùng không có quyền phê duyệt", "EmptyInfor": "<PERSON><PERSON><PERSON> việc không đầy đủ thông tin", "subPMNotFound": "SubPM không tồn tại", "employeeNotFound": "<PERSON><PERSON><PERSON> thi công không tồn tại", "CanNotDelete": "<PERSON><PERSON><PERSON>ng thể xóa công việc đã hoàn thành", "IdIsNull": "Id công vi<PERSON><PERSON> không hợp lệ", "CanNotUpdateSubPM": "Tr<PERSON><PERSON> đã có kết quả gửi lên không thể thay SubPM", "CanNotUpdateEmployee": "Trạm đã có kết quả gửi lên không thể thay đội kiểm tra", "ListIdNull": "<PERSON><PERSON> s<PERSON>ch id trống"}, "Approve": {"ApproveNotFound": "<PERSON><PERSON> không tồn tại"}, "Action": {"nameLengthIs255": "<PERSON>ên công việc không được dài quá 255 kí tự", "mustHavePosition": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> chưa có thứ tự sắp xếp", "IdIsNull": "Id công vi<PERSON><PERSON> không hợp lệ", "ActionNotFound": "<PERSON><PERSON>ng vi<PERSON><PERSON> không tồn tại", "CanNotUpdateTemplate": "<PERSON><PERSON><PERSON> việc có trạm có kết quả không thể đổi mẫu", "CanNotDelete": "<PERSON><PERSON><PERSON> việc có trạm có kết quả không thể xóa"}, "Station": {"nameIsNull": "Tên trạm không được bỏ trống", "nameLengthIs255": "Tên trạm không đ<PERSON><PERSON><PERSON> dài quá 255 kí tự", "codeIsNull": "<PERSON>ã trạm không được bỏ trống", "codeLengthIs255": "Mã trạm không đư<PERSON><PERSON> dài quá 255 kí tự", "areaIsNull": "Tỉnh/Tp của trạm không được bỏ trống", "districtIsNull": "<PERSON>uận/huyện của trạm không được bỏ trống", "latitudeIsNull": "<PERSON><PERSON> độ trạm không được bỏ trống", "latitudeLengthIs255": "<PERSON><PERSON> độ không đư<PERSON><PERSON> dài quá 255 kí tự", "longitudeLengthIs255": "<PERSON><PERSON> độ không đư<PERSON><PERSON> dài quá 255 kí tự", "ExistInAnotherContract": "Mã trạm đã tồn tại ở dự án khác"}, "Dashboard": {"filterInvalid": "<PERSON><PERSON><PERSON> kết thúc phải lớn hơn ngày kết thúc"}, "User": {"NotHavePermission": "<PERSON>ư<PERSON><PERSON> dùng không có quyền", "UserNoPermission": "<PERSON><PERSON><PERSON> kho<PERSON>n người dùng không có quyền", "ListUserEmpty": "<PERSON><PERSON> s<PERSON>ch tài kho<PERSON>n người dùng trống"}, "userAndPermission": {"existedAccount": "<PERSON>ail đã đư<PERSON><PERSON> đăng ký", "inactiveAccount": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã khóa"}, "AcceptanceDocs": {"acceptanceDocsExisted": "<PERSON><PERSON> sơ nghiệm thu đã tồn tại", "acceptanceDocsNotExists": "<PERSON><PERSON> sơ nghiệm thu không tồn tại"}, "Supplier": {"invalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại", "fileEmpty": "File không có dữ liệu hợp lệ, vui lòng kiểm tra lại", "NameExisted": "Tên nhà cung cấp đã tồn tại", "ShortNameExisted": "<PERSON><PERSON><PERSON><PERSON> tắt NCC đã tồn tại", "SupplierUsedByPo": "<PERSON>h<PERSON>ng thể chỉnh sửa NCC đã dùng cho PO", "erpNotExisted": "<PERSON><PERSON><PERSON> sản xuất chưa đ<PERSON><PERSON><PERSON> khai báo trên ERP", "fileInvalid": "File import không đúng mẫu, vui lòng kiểm tra lại", "SupplierNotFound": "<PERSON><PERSON><PERSON> cung cấp không tồn tại trên hệ thống"}, "Criteria": {"fileInvalid": "File import không đúng mẫu, vui lòng kiểm tra lại", "criteriaEmpty": "<PERSON><PERSON>à cung cấp chưa có bộ tiêu chí đánh giá phù hợp"}, "SupplierReport": {"FileReportQualityInvalid": "File báo c<PERSON>o chất lư<PERSON>ng không hợp lệ", "FileReportQualityIsNotNull": "File báo cáo chất lượng đang trống", "FileReportQualityIsNotNullData": "File báo cáo chất lượng không có dữ liệu", "FileReportQualityDeliveryInvalid": "File báo c<PERSON>o chất lượng giao hàng không hợp lệ", "FileReportQualityDeliveryIsNotNull": "File báo c<PERSON>o chất lư<PERSON>ng giao hàng đang trống", "FileReportQualityDeliveryIsNotNullData": "File báo cáo chất lượng giao hàng không có dữ liệu", "FileInvalid": "File import không đúng mẫu, vui lòng kiểm tra lại", "SupplierEmpty": "<PERSON><PERSON><PERSON> xu<PERSON>t báo cáo không có giao dịch"}, "Po": {"OrderNoIsExisted": "Số đơn hàng đã tồn tại", "SupplierInvalid": "<PERSON><PERSON><PERSON> cung cấp không hợp lệ", "PoCreatedTransfer": "<PERSON><PERSON><PERSON>ng thể xoá phiếu chuyển giao đã tạo PCG"}, "PoTransfer": {"dateIsNotNull": "<PERSON><PERSON><PERSON> chuy<PERSON>n giao đang trống", "transferImportDeleteNotNewOrCancel": "Chỉ có thể xoá phiếu chuyển giao ở trạng thái <PERSON>/ <PERSON><PERSON> hủy", "transferExportDeleteNotNewOrCancel": "Chỉ có thể xoá yêu cầu xuất kho ở trạng thái <PERSON>/ <PERSON><PERSON> hủy", "poExportIsEmpty": "<PERSON><PERSON> liệu xuất kho chưa tồn tại", "stateInvalid": "<PERSON><PERSON><PERSON> d<PERSON>ch tại kho đã hoàn thành, kh<PERSON><PERSON> cho phép chỉnh sửa", "hadTransferDetail": "<PERSON><PERSON><PERSON> d<PERSON>ch tại kho đã đ<PERSON><PERSON><PERSON> quét, kh<PERSON><PERSON> cho phép chỉnh sửa"}, "PoBoq": {"fileIsEmpty": "File import không có dữ liệu", "fileNotExcel": "File import sai đ<PERSON><PERSON> dạng, vui lòng thử lại với file excel", "fileInvalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại"}, "Logistic": {"typeInvalid": "<PERSON><PERSON><PERSON> hình nhà cung cấp logistic không đúng", "statusInvalid": "Trạng thái nhà cung cấp logistic không đúng", "fullNameIsExisted": "Tên đầy đủ nhà cung cấp logistic đã tồn tại", "shortNameIsExisted": "Tên viết tắt nhà cung cấp logistic đã tồn tại", "usedInBo": "NCC đã đư<PERSON><PERSON> chọn trong yêu cầu vận chuyển"}, "XSS": {"invalidData": "<PERSON><PERSON><PERSON> tượ<PERSON> chứa trường có dữ liệu không hợp lệ"}, "Maintenance": {"notHaveStation": "<PERSON><PERSON><PERSON><PERSON> không thuộc dự án đã chọn"}, "softwareResource": {"nameAndVersionMustBeUnique": "phần mêm đã tồn tại", "nameNotMatch": "Tên phần mềm không trùng khớp, vui lòng kiểm tra lại!", "versionNotChanged": "Version phần mêm đã tồn tại!. <PERSON><PERSON> lòng cập nhật lại version", "deleteFailed": "<PERSON><PERSON><PERSON> mềm không thể xóa vì đang đư<PERSON><PERSON> sử dụng trong hồ sơ sản phẩm", "existed": "<PERSON><PERSON>n mềm đã tồn tại, vui lòng kiểm tra lại!"}, "uploadFile": {"inValidVersionFile": "<PERSON><PERSON><PERSON> bản không hợp lệ. Version phải theo format m.n với m trong khoảng [1,1000], n trong khoảng [0,1000]", "inValidDocumentFile": "phần mêm đã tồn tại"}, "productLine": {"nameFound": "<PERSON><PERSON>ng sản phẩm đã tồn tại"}, "productModel": {"nameIsInUsed": "Tên model đ<PERSON> tồn tại"}, "productVersion": {"approvedCloneExists": "<PERSON><PERSON> sơ đã đư<PERSON><PERSON> phê du<PERSON>t", "alreadyTransferred": "<PERSON><PERSON> sơ này đã đ<PERSON><PERSON><PERSON> chuyển giao"}, "productDocument": {"bomNotApproved": "RDBOM chư<PERSON> phê du<PERSON>, kh<PERSON><PERSON> thể gửi phê duy<PERSON>, vui lòng kiểm tra lại", "fileReadError": "File import không đúng mẫu, vui lòng kiểm tra lại", "fileNotFound": "CAD file kh<PERSON><PERSON> hợ<PERSON> l<PERSON>, vui lòng kiểm tra lại"}, "MonthlyQuote": {"DateIsNotNull": "<PERSON><PERSON><PERSON> giá yêu cầu dữ liệu", "DateIsExists": "<PERSON><PERSON> tồn tại báo giá của tháng này", "fileIsEmpty": "File không có dữ liệu, vui lòng kiểm tra lại", "fileInvalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại", "emptyData": "File không có dữ liệu, vui lòng kiểm tra lại"}, "MonthlyExpenses": {"monthlyExpensesExisted": "NCC đã có thông tin chi phí của tháng này", "logisticIdInvalid": "NCC kh<PERSON>ng h<PERSON>p lệ", "logisticTypeInvalid": "Loại NCC không đúng", "invalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại", "hadMonthlyExpenses": "Lô vận chuyển trong file đã có thông tin chi phí tháng này, vui lòng xóa chi phí cũ trước khi import", "noLotAvailable": "<PERSON><PERSON>à cung cấp không có lô vận chuyển khả dụng"}, "PaymentLot": {"hadMonthlyExpenses": "Lô vận chuyển trong file đã có thông tin chi phí tháng này, vui lòng xóa chi phí cũ trước khi import"}, "Lot": {"NotFound": "<PERSON><PERSON> vận chuyển không tồn tại"}, "LotCustom": {"isExists": "Giai đoạn 4 đã tồn tại", "DuplicateCustomsNumber": "Trùng lặp số vận đơn hoặc số tờ khai"}, "BoGoodsArrive": {"isExists": "<PERSON><PERSON><PERSON> đo<PERSON>n 6 đã tồn tại"}, "BoPayTax": {"isExists": "Giai đoạn 5 đã tồn tại"}, "ShippingMethod": {"IsUsed": "<PERSON><PERSON><PERSON><PERSON> thức vận chuyển đã đư<PERSON><PERSON> gán với BO", "DuplicateName": "Tên ph<PERSON><PERSON><PERSON> thức vận chuyển đã tồn tại"}, "Bo": {"LotHaveInfo": "<PERSON><PERSON><PERSON> cầu vật chuyển đã cập nhật thông tin vận chuyển theo lô", "NotFound": "<PERSON><PERSON><PERSON> c<PERSON>u vận chuyển không tồn tại"}, "BoItem": {"wrongTemplateImportBoItems": "File import không đúng mẫu, vui lòng kiểm tra lại"}, "Rfq": {"invalidFormat": "File import không đúng mẫu, vui lòng kiểm tra lại"}, "SCBom": {"SCBomWasUsed": "SC Bom đã đư<PERSON><PERSON> sử dụng", "DuplicateCode": "Tên SC BOM đã tồn tại", "AccountingCodeNotNull": "<PERSON><PERSON> kế toán không được bỏ trống", "BomCodeNotNull": "<PERSON><PERSON> không được bỏ trống"}, "LogisticsEvaluate": {"FileInvalid": "File import không đúng mẫu, vui lòng kiểm tra lại"}, "LogisticsAgent": {"IsUsed": "<PERSON><PERSON><PERSON> lý đã đư<PERSON><PERSON> sử dụng trong yêu cầu vận chuyển"}, "AdditionalFee": {"DuplicateName": "Tên phí đã tồn tại"}}, "welcome": {"header": "<PERSON><PERSON><PERSON> m<PERSON>ng", "message": "<PERSON>n chào, {{ value }}"}, "message": {"success": "<PERSON><PERSON><PERSON><PERSON> công", "info": "Thông tin", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "error": "Lỗi"}, "validate": {"required": "Trư<PERSON><PERSON> này là trường b<PERSON>t buộc", "min": "<PERSON><PERSON><PERSON> trị không được nhỏ hơn {{ min }}", "max": "<PERSON><PERSON><PERSON> trị không đ<PERSON><PERSON> lớn hơn {{ max }}", "email": "<PERSON><PERSON><PERSON> d<PERSON>ng email không hợp lệ", "minlength": "<PERSON><PERSON> dài tối thiểu là {{ minlength }} ký tự", "maxlength": "<PERSON><PERSON> dài tối đa là {{ maxlength }} ký tự", "maxdaterange": "<PERSON><PERSON><PERSON><PERSON> thời gian tối đa là {{ max }} {{ type }}", "emptyArray": "<PERSON><PERSON><PERSON> c<PERSON>u mảng dữ liệu"}}