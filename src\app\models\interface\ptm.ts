import { BaseEntity } from '../BaseEntity';

export interface ProcessFlow extends BaseEntity {
    versionId: number;
    phase: number;
    reviewerIds: number[];
    listProcessFlow: ProcessFlowDetail[];
    processFlows: object;
}

export interface ProcessFlowDetail extends BaseEntity {
    id: number;
    processFlowId: number;
    step: number;
    oc: string;
    nextOc: string;
    symbol: string;
    operationDescription: string;
    productAndProcessCharacteristics: string;
    online: boolean;
    offline: boolean;
}
export interface Routing extends BaseEntity {
    listRouting: RoutingDetail[];
}
// SOP-BASE

export interface SectionEntrySopBase {
    id: number;
    STT: number;
    OC: string;
    mainOperationBlock: string;
    operationDescription: string;
    focusLevel: number;
    workArea: string;
    equipmentName: string;
    equipmentQty: number;
    materialCode: string;
    materialName: string;
    materialQty: string;
    workStationNotes?: string;
    details?: string;
    basicOperationTime?: string;
}

export interface SectionSobBase {
    /** <PERSON><PERSON> kh<PERSON>i, ví dụ "SMTTOP", "SMTBOT", ... */
    code: string;
    /** <PERSON>h sách dòng của khối */
    entries: SectionEntrySopBase[];
}

export interface RoutingDetail extends BaseEntity {
    id: number;
    routingId: number;
    step: number;
    operationDescription: string;
    operationTime: number;
    operationUnit: string;
    operationQuantity: number;
    operationCost: number;
    operationResource: string;
}

export interface ManBom extends BaseEntity {
    listManBom: ManBomDetail[];
}

export interface ManBomDetail extends BaseEntity {
    id: number;
    manBomId: number;
    description: number;
    unit: string;
    process: number;
    quantity: string;
    reference: string;
    materialType: number;
    attritionRate: string;
    note: string;
}

export interface OtherDoc {
    rowId: number;
    id: number;
    name: string;
    file: File | null;
    lastUpdate: string | null;
    versionSelected: number | null;
    filePath: string;
    updatedBy: number | null;
    action: 0 | 1 | 2 | 3;
}
export interface OtherDocRequest {
    id?: number;
    name?: string;
    filePath?: string;
    action: 0 | 1 | 2 | 3;
}
export interface OtherDocsSaveData {
    instructionId: string;
    payload: UpdateOtherDocsPayload;
}
export interface UpdateOtherDocsPayload {
    productVersionId: number;
    lifecycleStage: number;
    otherDocuments: OtherDocRequest[];
}
export interface CLDoc {
    id: number;
    name: string;
    formExample: string | null;
    lastUpdate: string | null;
    status: number | null;
    versionSelected: number | null;
    updatedBy: number | null;
}
