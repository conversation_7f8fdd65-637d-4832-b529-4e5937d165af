<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <ng-container *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'version_create']">
            <p-button
                *ngIf="currentProduct && designMaterials.productRecordSelected"
                [routerLink]="['create', currentProduct.id, designMaterials.productRecordSelected.id]"
                [state]="{ currentProduct: currentProduct }"
                label="Thêm mới"
            />
        </ng-container>
    </ng-template>
</app-sub-header>

<div style="padding: 1rem 1rem 0 1rem">
    <!-- filter -->
    <app-form [formGroup]="filterForm" (onSubmit)="onSearch($event)" layout="vertical">
        <div class="tw-grid tw-grid-cols-[2fr_2fr_2fr_1fr] tw-gap-4 tw-my-4">
            <!-- Dòng sản phẩm -->

            <app-custom-form-item [noGrid]="true" label="Dòng sản phẩm">
                <app-combobox-nonRSQL
                    #lineSelect
                    [fetchOnInit]="true"
                    type="select-one"
                    formControlName="productLine"
                    fieldValue="id"
                    fieldLabel="name"
                    url="/pr/api/product-line/filter"
                    param="name"
                    placeholder="Chọn dòng sản phẩm"
                    [additionalParams]="{ size: 100 }"
                    (onChange)="onProductLineSelect($event)"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <app-custom-form-item [noGrid]="true" label="Model" [control]="filterForm.get('model')">
                <app-combobox-nonRSQL
                    #modelSelectFilter
                    [fetchOnInit]="true"
                    type="select-one"
                    formControlName="model"
                    fieldValue="id"
                    fieldLabel="name"
                    url="/pr/api/product-model"
                    param="name"
                    placeholder="Chọn Model"
                    (onChange)="onChangeModel($event)"
                    [additionalParams]="modelParams"
                >
                </app-combobox-nonRSQL>
            </app-custom-form-item>

            <!-- Tên sản phẩm -->

            <app-custom-form-item [noGrid]="true" label="Sản phẩm" [control]="filterForm.get('productId')">
                <app-combobox-nonRSQL
                    #productSelect
                    [fetchOnInit]="true"
                    type="select-one"
                    formControlName="productId"
                    fieldValue="id"
                    [fieldLabel]="['name', 'vnptManPn']"
                    url="/pr/api/product"
                    [additionalParams]="productParams"
                    param="searchNameOrManPn"
                    placeholder="Tên sản phẩm - VNPT Man P/N"
                    (onChange)="onProductSelect($event)"
                ></app-combobox-nonRSQL>
            </app-custom-form-item>

            <div>
                <div style="height: 28px"></div>
                <button pButton class="p-button-rounded p-button-secondary" type="submit" icon="pi pi-search"></button>
            </div>
        </div>
    </app-form>
    <!-- product-info -->
    <app-product-info
        header="Thông tin sản phẩm"
        [fieldsLeft]="[
            { label: 'Dòng sản phẩm', value: 'lineName' },
            { label: 'Tên sản phẩm', value: 'name' },
            { label: 'Mô tả', value: 'description' },
            { label: 'VNPT P/N', value: 'vnptManPn' },
            { label: 'HW version', value: 'hardwareVersion' },
            { label: 'FW version', value: 'firmwareVersion' },
        ]"
        [fieldsRight]="[
            { label: 'Tên thương mại', value: 'tradeName' },
            { label: 'Mã thương mại', value: 'tradeCode' },
            { label: 'Model', value: 'modelName' },
            { label: 'Generation', value: 'generation' },
            { label: 'Hình ảnh sản phẩm', value: 'imageName', type: 'link', url: 'imageUrl' },
            { label: 'Giai đoạn', value: 'stage', type: 'tag' },
        ]"
        [product]="currentProduct"
    ></app-product-info>
    <!-- Design Document -->
    <ng-container>
        <app-product-info
            header="Tư liệu thiết kế"
            [fieldsLeft]="[
                {
                    label: 'Version HSSP',
                    value: 'id',
                    type: 'select',
                    editable: true,
                    options: designMaterials.listVersion,
                },
                { label: 'Giai đoạn HSSP', value: 'lifecycleStageText', type: 'tag' },
                { label: 'Trạng thái HSSP', value: 'statusText' },
                { label: 'Link tư liệu thiết kế', value: 'imageName', type: 'link', url: 'imageUrl' },
            ]"
            [product]="designMaterials.productRecordSelected"
            [forceShow]="designMaterials.listVersion?.length > 0"
            [emptyMessage]="'Vui lòng chọn tư liệu thiết kế để tiếp tục'"
            (fieldChanged)="onFieldChanged($event)"
        ></app-product-info>
    </ng-container>
    <!-- Action Buttons -->
    <div class="action-buttons">
        <button
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'version_compare']"
            pButton
            type="button"
            (click)="onOpenComparePopup($event)"
            label="So sánh"
            class="p-button-sm p-button-info"
        ></button>

        <button
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'version_history']"
            pButton
            type="button"
            (click)="onOpenHistoryVersion()"
            label="Lịch sử version HSSP"
            class="p-button-sm p-button-info"
        ></button>

        <!-- <button  pButton type="button" label="Thêm mới" class="p-button-sm p-button-success"></button> -->
    </div>
    <!-- product-version -->
    <app-product-version
        #ProductVersion
        [currentProduct]="currentProduct"
        (openTransferPopup)="onOpenTransfer($event)"
        (openApprovalPopup)="onOpenApproval($event)"
        (openSendApprovalPopup)="onOpenSendApproval($event)"
        (openHistoryChangedPopup)="onOpenHistoryChanged($event)"
        (openLifecycleStagePopup)="onLifecycleStagePopup($event)"
        (openClonePopup)="onOpenClonePopup($event)"
        (loadVersionProfiles)="loadVersionProfiles()"
    ></app-product-version>
    <!-- popup-history-version -->
    <p-dialog
        header="Lịch sử version HSSP"
        [(visible)]="visibleHistoryVersion"
        [modal]="true"
        [style]="{ width: '60vw' }"
        (onHide)="close('visibleHistoryVersion')"
    >
        <p-table [value]="records" [responsiveLayout]="'scroll'" [paginator]="true" [rows]="5" class="p-datatable-sm">
            <ng-template pTemplate="header">
                <tr>
                    <th>Ngày</th>
                    <th>Version</th>
                    <th>Người thực hiện</th>
                    <th>Nội dung cập nhật</th>
                    <th>Version nhân bản</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item>
                <tr>
                    <td>{{ item.date }}</td>
                    <td>{{ item.version }}</td>
                    <td>{{ item.author }}</td>
                    <td>{{ item.updateContent }}</td>
                    <td>{{ item.cloneVersion || '-' }}</td>
                </tr>
            </ng-template>
        </p-table>

        <ng-template pTemplate="footer">
            <div class="p-d-flex p-jc-end p-gap-2">
                <p-button
                    [disabled]="!records || records.length === 0"
                    label="Xuất excel"
                    size="small"
                    severity="success"
                    (click)="onOpenExportExcel()"
                ></p-button>
                <p-button label="Đóng" [text]="true" [raised]="true" size="small" severity="secondary" (click)="close('visibleHistoryVersion')"></p-button>
            </div>
        </ng-template>
    </p-dialog>

    <!-- popup-transfer -->

    <p-dialog
        [header]="'Chuyển giao hồ sơ'"
        [(visible)]="isVisibleTransferPopup"
        [draggable]="false"
        [modal]="true"
        [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
        [style]="{ width: '50vw' }"
        (onHide)="closeDialogTransfer()"
    >
        <hr style="margin: 0" />
        <br />
        <app-form [formGroup]="formTransferPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formTransferPopup">
            <div class="tw-grid tw-gap-4">
                <app-custom-form-item [control]="formTransferPopup.get('toEmails')" label="Chọn người nhận thông báo:">
                    <app-combobox-nonRSQL
                        #userSelect
                        [fetchOnInit]="false"
                        type="select"
                        formControlName="toEmails"
                        fieldValue="email"
                        fieldLabel="email"
                        url="/auth/api/users/simple-search"
                        param="query"
                        [additionalParams]="{ page: 0, size: 100 }"
                        placeholder="Chọn người dùng"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item label="Ghi chú:">
                    <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
                </app-custom-form-item>
            </div>
        </app-form>
        <ng-template pTemplate="footer">
            <p-button severity="primary" type="submit" [disabled]="formTransferPopup ? formTransferPopup.invalid : false" (click)="submitFormTransfer(1)"
                >Xác nhận
            </p-button>
            <p-button severity="primary" type="submit" (click)="submitFormTransfer(0)">Bỏ qua và thực hiện chuyển giao </p-button>
        </ng-template>
    </p-dialog>

    <!--    <app-popup-->
    <!--        #TransferPopup-->
    <!--        header="Chuyển giao hồ sơ"-->
    <!--        severity="success"-->
    <!--        [isButtonVisible]="isButtonVisible"-->
    <!--        [showConfirmButton]="false"-->
    <!--        [showFooterDialog]="false"-->
    <!--        [formGroup]="formTransferPopup"-->
    <!--        (onSubmit)="submitFormTransfer($event)"-->
    <!--    >-->
    <!--        <app-form [formGroup]="formTransferPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4"-->
    <!--                  *ngIf="formTransferPopup">-->
    <!--            <div class="tw-grid tw-gap-4 ">-->
    <!--                <app-custom-form-item [control]="formTransferPopup.get('approvers')" label="Chọn người nhận thông báo:">-->
    <!--                    <app-combobox-nonRSQL-->
    <!--                        #userSelect-->
    <!--                        [fetchOnInit]="false"-->
    <!--                        type="select"-->
    <!--                        formControlName="approvers"-->
    <!--                        fieldValue="id"-->
    <!--                        fieldLabel="email"-->
    <!--                        url="/auth/api/users/simple-search"-->
    <!--                        param="query"-->
    <!--                        [additionalParams]="{ page: 0, size: 100 }"-->
    <!--                        placeholder="Chọn người dùng"-->
    <!--                    >-->
    <!--                    </app-combobox-nonRSQL>-->
    <!--                </app-custom-form-item>-->
    <!--                <app-custom-form-item label="Ghi chú:">-->
    <!--                    <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note"-->
    <!--                              maxlength="1000"></textarea>-->
    <!--                </app-custom-form-item>-->
    <!--            </div>-->
    <!--            <div class="tw-flex tw-justify-end tw-gap-4">-->
    <!--                <p-button-->
    <!--                    severity="primary"-->
    <!--                    type="submit"-->
    <!--                    [disabled]="formTransferPopup ? formTransferPopup.invalid : false"-->
    <!--                    (click)="submitFormTransfer($event)"-->
    <!--                >Xác nhận-->
    <!--                </p-button>-->
    <!--                <p-button-->
    <!--                    severity="primary"-->
    <!--                    type="submit"-->
    <!--                    (click)="submitFormTransfer($event)"-->
    <!--                >Bỏ qua và thực hiện chuyển giao-->
    <!--                </p-button>-->
    <!--            </div>-->
    <!--        </app-form>-->
    <!--    </app-popup>-->

    <!-- popup-send-approval -->
    <app-popup
        [dialogWidth]="'960px'"
        #SendApprovalPopup
        header="Gửi phê duyệt HSSP"
        severity="success"
        [isButtonVisible]="isButtonVisible"
        [formGroup]="formSendApprovalPopup"
        (onSubmit)="submitFormSendApproval($event)"
    >
        <app-form [formGroup]="formSendApprovalPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formSendApprovalPopup">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-custom-form-item [control]="formSendApprovalPopup.get('email')" label="Người phê duyệt:">
                    <app-combobox-nonRSQL
                        #userApproval
                        [fetchOnInit]="false"
                        type="select-one"
                        formControlName="email"
                        fieldValue="email"
                        fieldLabel="email"
                        url="/auth/api/users/simple-search"
                        param="query"
                        [additionalParams]="{ page: 0, size: 100 }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>

                <app-custom-form-item [labelCol]="'tw-col-span-5'" [wrapperCol]="'tw-col-span-7'" label="Người gửi phê duyệt:">
                    <span *ngIf="user$ | async as user" class="tw-leading-8">{{ user.email }} </span>
                </app-custom-form-item>
                <app-custom-form-item [control]="formSendApprovalPopup.get('cc')" label="CC:">
                    <app-combobox-nonRSQL
                        #userCC
                        [fetchOnInit]="false"
                        type="select"
                        formControlName="ccEmail"
                        fieldValue="email"
                        fieldLabel="email"
                        url="/auth/api/users/simple-search"
                        param="query"
                        [additionalParams]="{ page: 0, size: 100 }"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
                <app-custom-form-item [labelCol]="'tw-col-span-5'" [wrapperCol]="'tw-col-span-7'" label="Ngày gửi phê duyệt:">
                    <span class="tw-leading-8">{{ today | date: 'dd/MM/yyyy' }}</span>
                </app-custom-form-item>
                <app-custom-form-item label="Ghi chú:">
                    <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
                </app-custom-form-item>
            </div>
        </app-form>
    </app-popup>
    <!-- popup-approval -->
    <p-dialog header="Phê duyệt HSSP" [(visible)]="visibleApprovalPopup" [modal]="true" [style]="{ width: '70vw' }" (onHide)="close('visibleApprovalPopup')">
        <app-form [formGroup]="formApprovalPopup" styleClass="tw-grid tw-grid-cols-1 tw-gap-4" *ngIf="formApprovalPopup">
            <div class="tw-grid tw-grid-cols-2 tw-gap-4">
                <app-custom-form-item label="Ghi chú:">
                    <span *ngIf="formApprovalPopup.get('noteOfSendApproval')?.value" class="tw-leading-8">
                        {{ formApprovalPopup.get('noteOfSendApproval')?.value }}
                    </span></app-custom-form-item
                >

                <app-custom-form-item label="Người gửi phê duyệt:">
                    <span *ngIf="formApprovalPopup.get('fromUser')?.value" class="tw-leading-8">{{ formApprovalPopup.get('fromUser')?.value }}</span>
                </app-custom-form-item>
                <a href="#" (click)="onViewHistory($event)" style="cursor: pointer; text-decoration: none"> Xem lịch sử version HSSP </a>
                <app-custom-form-item label="Ngày gửi phê duyệt:">
                    <span *ngIf="formApprovalPopup.get('created')?.value" class="tw-leading-8">{{
                        formApprovalPopup.get('created')?.value | date: 'dd/MM/yyyy'
                    }}</span>
                </app-custom-form-item>
                <app-custom-form-item [control]="formApprovalPopup.get('note')" label="Ghi chú (người phê duyệt):">
                    <textarea rows="5" class="tw-w-full" pInputTextarea [autoResize]="true" formControlName="note" maxlength="1000"></textarea>
                </app-custom-form-item>
            </div>
        </app-form>

        <ng-template pTemplate="footer">
            <div class="tw-flex tw-gap-2 tw-justify-center">
                <p-button label="Phê duyệt" size="small" severity="success" (click)="submitFormApproval('CONFIRM')"></p-button>

                <p-button label="Từ chối" size="small" styleClass="p-button-danger" (click)="submitFormApproval('REJECT')"></p-button>
            </div>
        </ng-template>
    </p-dialog>

    <!-- popup-export-version -->
    <app-popup
        #ExportFilePopup
        header="Xuất file"
        severity="success"
        [dialogWidth]="'30vw'"
        [isButtonVisible]="isButtonVisible"
        [formGroup]="formExportFilePopup"
        (onSubmit)="submitFileTransfer($event)"
    >
        <app-form
            [labelCol]="'tw-col-span-7'"
            [wrapperCol]="'tw-col-span-5'"
            [formGroup]="formExportFilePopup"
            styleClass="tw-grid tw-grid-cols-1 tw-gap-5"
            *ngIf="formExportFilePopup"
        >
            <app-custom-form-item label="Xuất đến version" [control]="formExportFilePopup.get('selectedVersion')">
                <p-dropdown [options]="versionOptions" optionLabel="label" optionValue="value" appendTo="body" formControlName="selectedVersion" />
            </app-custom-form-item>
        </app-form>
    </app-popup>
    <!-- popup-history-changed -->
    <app-change-history-popup [(visible)]="visibleHistoryChanged" [dialogWidth]="'80vw'" [records]="historyRecords"></app-change-history-popup>
    <!-- popup-clone -->
    <app-popup #ClonePopup header="Chọn mục nhân bản" [isButtonVisible]="isButtonVisible" [formGroup]="cloneForm" (onSubmit)="submitClonePopup($event)">
        <p-table [value]="cloneItems">
            <ng-template pTemplate="body" let-item let-i="rowIndex">
                <tr>
                    <td style="width: 2rem; text-align: center">
                        <p-checkbox
                            [(ngModel)]="item.selected"
                            [binary]="true"
                            [ngModelOptions]="{ standalone: true }"
                            (onChange)="onCheckboxChange(i, $event.checked)"
                            [disabled]="item.disabled"
                        ></p-checkbox>
                    </td>
                    <td>{{ item.label }}</td>
                </tr>
            </ng-template>
        </p-table>
    </app-popup>
    <!-- popup so sánh hồ sơ -->
    <app-popup
        #ComparePopup
        header="So sánh hồ sơ"
        [isButtonVisible]="isButtonVisible"
        [dialogWidth]="'80vw'"
        [showConfirmButton]="false"
        (onClose)="handlePopupClose()"
    >
        <app-compare-profile #compareProfileComp [versions]="selectedVersions"></app-compare-profile>
        <!--  -->
    </app-popup>

    <!-- popup phê duyệt MP/Pilot -->
    <app-popup
        #LifecycleStagePopup
        header="Chuyển giai đoạn sản xuất"
        [isButtonVisible]="isButtonVisible"
        (onSubmit)="submitLifeCycleStage($event, selectedStageType)"
    >
        <div class="tw-text-center">
            Hồ sơ sản phẩm version
            <span *ngIf="selectedVersion">{{ selectedVersion.version }}</span>
            sẽ được chuyển sang giai đoạn
            <span *ngIf="selectedStageType">{{ selectedStageType }}</span
            ><br />
            Vui lòng xác nhận thông tin!
        </div>
    </app-popup>
</div>
