import { canAuthorize } from '../../../core/auth/auth.guard';

export const MTRRouting = {
    path: 'manufacturing-technology-records',
    title: '<PERSON><PERSON> sơ CNSX',
    canActivate: [canAuthorize],
    data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
    children: [
        {
            path: '',
            title: '<PERSON><PERSON> s<PERSON>ch hồ sơ CNSX',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./list/list.component').then((c) => c.MfgTechRecordsListComponent),
        },
        {
            path: 'create/:productId/:productRecordId',
            title: 'Tạo mới hồ sơ CNSX',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./edit/mfg-tech-records.edit.component').then((c) => c.MfgTechRecordsEditComponent),
        },
        {
            path: 'edit/:productId/:versionId',
            title: 'Chỉnh sử<PERSON> hồ sơ CNSX',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./edit/mfg-tech-records.edit.component').then((c) => c.MfgTechRecordsEditComponent),
        },
        {
            path: 'view/:productId/:versionId',
            title: 'Xem chi tiết hồ sơ CNSX',
            data: { authorize: ['ROLE_SYSTEM_ADMIN'] },
            loadComponent: () => import('./edit/mfg-tech-records.edit.component').then((c) => c.MfgTechRecordsEditComponent),
        },
    ],
};
