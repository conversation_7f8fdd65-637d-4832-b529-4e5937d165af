// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';

import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { OtherDoc, OtherDocRequest, OtherDocsSaveData, UpdateOtherDocsPayload } from 'src/app/models/interface/ptm';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { MfgTechRecordService } from 'src/app/services/ptm/manufacturing-technology-records/mfg-tech-records.service';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { v4 as uuid } from 'uuid';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { combineLatest, finalize, map, Subscription, switchMap, tap } from 'rxjs';
import { UploadService } from 'src/app/services/upload/upload.service';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';

@Component({
    selector: 'app-other-docs',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        UploadCustomComponent,
        TabActionButtonsComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './other-docs.component.html',
    styleUrls: ['./other-docs.component.scss'],
    providers: [DatePipe, FormActionService],
})
export class OtherDocsComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống

    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() version: { status: number } = { status: 1 };
    @Input() productVersionId: number;
    @Input() lifecycleStage: number;
    // 📤 OUTPUTS emit về cha

    @Output() changed = new EventEmitter<string>();
    @Output() submitted = new EventEmitter<OtherDoc[]>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @ViewChildren('OtherDocsUploader') otherDocsUploader!: QueryList<UploadCustomComponent>;
    formGroup!: FormGroupCustom<{ items: OtherDoc[] }>;
    private uploadIds = new Map<number, string>();
    private subs = new Map<number, Subscription>();
    public loadingRows = new Map<number, boolean>();
    public isUploading = false;
    private uploadSub!: Subscription;
    instructionId: string;
    // Kết hợp hai stream thành một Observable<boolean>
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );

    constructor(
        private fb: FormBuilder,
        private mtrs: MfgTechRecordService,
        public formSvc: FormActionService<any>,
        private authService: AuthService,
        private datePipe: DatePipe,
        private fileUploadManagerService: FileUploadManagerService,
        private uploadService: UploadService,
    ) {}
    ngOnInit() {
        this.uploadSub = this.fileUploadManagerService.isUploading$.subscribe((flag) => (this.isUploading = flag));
        // 1) Build formGroupCustom
        this.formGroup = new FormGroupCustom(this.fb, {
            items: new FormArrayCustom([
                this.createRow({ name: 'Layout Line SX' }, true),
                this.createRow({ name: 'HDSD setup' }, true),
                this.createRow({ name: 'HDSD sửa chữa' }, true),
            ]),
        });

        // (2) gán form vào service
        this.formSvc.initialize({
            saveApi: (data: OtherDocsSaveData) => this.mtrs.saveOtherDocs(data.instructionId, data.payload),
            // nếu có submitApi / approveApi thì thêm vào đây
        });
    }
    get items(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('items') as FormArrayCustom<FormGroup>;
    }
    private createRow(data?: Partial<OtherDoc>, disable = false) {
        const id = uuid();
        return this.fb.group({
            rowId: [data?.id || 0],
            id: [id],
            name: [{ value: data?.name || '', disabled: disable }, Validators.required],
            file: [data?.file || null],
            lastUpdate: [data?.lastUpdate || null],
            updatedBy: [data?.updatedBy || ''],
            filePath: [data?.filePath || ''],
            // action: 1=create, 2=update, 3=delete
            action: [data?.rowId ? 2 : 1],
        });
    }
    ngOnChanges(changes: SimpleChanges): void {}

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    addItem() {
        this.items.push(this.createRow());
    }

    removeItem(rowId: number) {
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')!.value === rowId);
        if (idx === -1) return;

        const ctrl = this.items.at(idx);

        // Nếu dòng mới (action=1) thì xoá hẳn,
        // ngược lại chỉ đánh dấu xóa để vẫn gửi action=3
        const currentAction = ctrl.get('action')!.value;
        if (currentAction === 1) {
            // mới thêm, chưa sync với BE → xoá hẳn
            this.items.removeAt(idx);
        } else {
            // đã tồn tại trên BE → mark delete
            ctrl.get('action')!.setValue(3);
        }

        // map loadingRows, uploadIds xoá sạch
        this.uploadIds.delete(rowId);
        this.loadingRows.delete(rowId);
    }
    onClearFile(rowId: number) {
        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        // Reset controls for this row
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx !== -1) {
            const ctrl = this.items.at(idx);
            ctrl.patchValue({ file: null, lastUpdate: null, updatedBy: '' });
        }
    }
    onFileSelected(files: any[], rowId: number) {
        const file = files[0];
        const fileName: string = files[0].name;
        if (!file) return;
        this.loadingRows.set(rowId, true);
        const uploadId = uuid();
        this.uploadIds.set(rowId, uploadId);

        const currentUser = this.authService.getPrinciple();
        // Find the row form group by id
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;
        const ctrl = this.items.at(idx);

        ctrl.patchValue({
            file,
            lastUpdate: this.datePipe.transform(new Date(), 'hh:mm dd/MM/yyyy'),
            updatedBy: currentUser?.email || '',
        });
        const sub = this.uploadService
            .analyzeFile(fileName, 'DOCUMENT')

            .pipe(
                tap((meta) => {
                    if (ctrl.get('filePath')) ctrl.get('filePath')?.setValue(meta.objectPath);
                }),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(files[0], meta.presignedUrl)),
                // dù complete hay error, luôn finish uploadId
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (p) => ({}),
                error: (e) => {
                    const list = this.otherDocsUploader.toArray();
                    if (list[idx]) list[idx].clearAll();
                },
                complete: () => {
                    this.loadingRows.set(rowId, false);
                },
            });
        this.subs.set(rowId, sub);
        // Hook cancel callback
        this.fileUploadManagerService.start(uploadId, () => {
            const s = this.subs.get(rowId);
            if (s) {
                s.unsubscribe();
                this.subs.delete(rowId);
            }
        });
    }

    onSave() {
        if (this.formGroup.invalid) {
            return;
        } else {
            const docs: OtherDoc[] = this.formGroup.value.items;
            // Map sang DTO
            const otherDocuments: OtherDocRequest[] = docs
                .filter((d) => d.action !== 0) // hoặc tuỳ logic, giữ lại các row cần gửi
                .map((d) => ({
                    id: d.id,
                    name: d.name,
                    filePath: d.filePath,
                    action: d.action,
                }));

            const payload: UpdateOtherDocsPayload = {
                productVersionId: this.productVersionId,
                lifecycleStage: this.lifecycleStage,
                otherDocuments,
            };
            console.log('payload', payload);
            const data: OtherDocsSaveData = {
                payload: payload,
                instructionId: this.instructionId || '0',
            };
            this.formSvc.save(data).subscribe({
                next: (resp: OtherDoc[]) => {
                    // đây là dữ liệu trả về từ BE
                    console.log('Saved OK, server returned', resp);

                    // Ví dụ side-effect: cập nhật lại UI, emit sự kiện, show toast…
                    this.submitted.emit(resp);
                    this.formGroup.resetForm();
                },
                error: (err) => {
                    console.error('Save failed', err);
                },
            });
            return;
        }
    }

    /** Kéo xử lý thành công thì emit và reset */
    // protected onSubmit(value) {
    //     console.log('value form submit', value);
    //     console.log('this.items.value', this.items.value);
    //     this.formGroup.resetForm();
    // }
    ngOnDestroy(): void {
        this.uploadSub.unsubscribe();
        console.log('🧹 [other-docs] Unmounted');
    }
}
