import { BaseEntity } from '../../BaseEntity';

export interface Routing extends BaseEntity {
    routingId: number;
    phase: number;
    chartUrl: string;
}

export interface RoutingDetail extends BaseEntity {
    id: number;
    routingId: number;
    step: number;
    operationDescription: string;
    operationTime: number;
    operationUnit: string;
    operationQuantity: number;
    operationCost: number;
    operationResource: string;
}
