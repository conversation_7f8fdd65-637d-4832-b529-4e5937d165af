@layer tailwind-base, primeng, tailwind-utilities;

@layer tailwind-base {
    @tailwind base;
}

@layer tailwind-utilities {
    @tailwind components;
    @tailwind utilities;
}
$gutter: 1rem; //for primeflex grid system
// @import 'assets/layout/styles/layout.scss';

/* PrimeNG */
// $prefix: 'p-'
// @import '../node_modules/primeng/resources/primeng.min.css';
// @import '../node_modules/primeflex/primeflex.scss';
@import '../node_modules/primeicons/primeicons.css';

/* Custom style */
@import 'assets/styles/custom/custom.scss';
@import 'assets/styles/main/main.scss';
@import '~@ng-select/ng-select/themes/default.theme.css';

@import 'material-icons/iconfont/material-icons.css';

body {
    min-height: 100%;
    font:
        400 14px/1,
        428571428571429 Roboto,
        sans-serif;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

