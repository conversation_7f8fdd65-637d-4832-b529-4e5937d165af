import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';

@Component({
    selector: 'app-info-share',
    standalone: true,
    imports: [CommonModule, FormsModule, InputTextareaModule],
    templateUrl: './info-share.component.html',
    styleUrls: ['./info-share.component.scss'],
})
export class InfoShareComponent implements OnInit, OnDestroy {
    ngOnDestroy(): void {
        console.log('🧹 [InfoShareComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [InfoShareComponent] Init');
    }
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';

    getInfo(): string {
        if (this.title === 'MANBOM') {
            return 'Ngư<PERSON>i phê duyệt: ';
        } else {
            return 'Ngư<PERSON>i soát xét: ';
        }
    }
}
