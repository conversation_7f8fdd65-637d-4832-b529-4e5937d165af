import { BaseEntity } from '../../BaseEntity';

export interface ProcessFlow extends BaseEntity {
    versionId: number;
    phase: number;
    reviewerIds: number[];
    listProcessFlow: ProcessFlowDetail[];
    processDetails: [];
}

export interface ProcessFlowDetail extends BaseEntity {
    id: number;
    processFlowId: number;
    step: number;
    oc: string;
    nextOc: string;
    symbol: string;
    operationDescription: string;
    productAndProcessCharacteristics: string;
    online: boolean;
    offline: boolean;
}
