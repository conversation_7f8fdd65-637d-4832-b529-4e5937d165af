// src/app/components/process-flow.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, SimpleChanges, QueryList, ViewChildren } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';

import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { UploadCustomComponent } from 'src/app/shared/components/upload-custom/upload-custom.component';
import { MfgTechRecordService } from 'src/app/services/ptm/manufacturing-technology-records/mfg-tech-records.service';
import { TabActionButtonsComponent } from '../tab-action-buttons/tab-action-buttons.component';
import { v4 as uuid } from 'uuid';
import { FormActionService } from 'src/app/services/ptm/manufacturing-technology-records/form-action.service';
import { AuthService } from 'src/app/core/auth/auth.service';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { combineLatest, finalize, map, Subscription, switchMap, tap } from 'rxjs';
import { UploadService } from 'src/app/services/upload/upload.service';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';
import { CLDoc } from 'src/app/models/interface/ptm';
import { FullscreenToggleDirective } from 'src/app/shared/directives/fullscreen-toggle.directive';

@Component({
    selector: 'app-other-docs',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        UploadCustomComponent,
        TabActionButtonsComponent,
        ComboboxNonRSQLComponent,
        FullscreenToggleDirective,
    ],
    templateUrl: './CL-docs.component.html',
    styleUrls: ['./CL-docs.component.scss'],
    providers: [DatePipe, FormActionService],
})
export class CLDocsComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống

    @Input() mode: 'view' | 'create' | 'edit' = 'create';
    @Input() version: { status: number } = { status: 1 };

    // 📤 OUTPUTS emit về cha

    @Output() changed = new EventEmitter<string>();
    @Output() submitted = new EventEmitter<CLDoc[]>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @ViewChildren('OtherDocsUploader') otherDocsUploader!: QueryList<UploadCustomComponent>;
    formGroup!: FormGroupCustom<{ items: CLDoc[] }>;
    private uploadIds = new Map<number, string>();
    private subs = new Map<number, Subscription>();
    public loadingRows = new Map<number, boolean>();
    public isUploading = false;
    private uploadSub!: Subscription;
    // Kết hợp hai stream thành một Observable<boolean>
    public isBusy$ = combineLatest([this.formSvc.isSaving$, this.fileUploadManagerService.isUploading$]).pipe(
        map(([saving, uploading]) => saving || uploading),
    );

    versionConfigs = [
        {
            url: '/pr/api/product-line/filter',
            fieldValue: 'id',
            fieldLabel: 'name',
            param: 'name',
        },
        {
            url: '/pr/api/sys_part_manufacturer/unused',
            fieldValue: 'id',
            fieldLabel: 'vnptManPn',
            param: 'vnptManPn',
        },
        {
            url: '/pr/api/software-resource/filter',
            fieldValue: 'id',
            fieldLabel: 'name',
            param: 'name',
        },
    ];
    constructor(
        private fb: FormBuilder,
        private mtrs: MfgTechRecordService,
        public formSvc: FormActionService<{ items: CLDoc[] }>,
        private authService: AuthService,
        private datePipe: DatePipe,
        private fileUploadManagerService: FileUploadManagerService,
        private uploadService: UploadService,
    ) {}
    ngOnInit() {
        this.uploadSub = this.fileUploadManagerService.isUploading$.subscribe((flag) => (this.isUploading = flag));
        // 1) Build formGroupCustom
        this.formGroup = new FormGroupCustom(this.fb, {
            items: new FormArrayCustom([
                this.createRow({ name: 'Layout Line SX' }, true),
                this.createRow({ name: 'HDSD setup' }, true),
                this.createRow({ name: 'HDSD sửa chữa' }, true),
            ]),
        });

        // (2) gán form vào service
        this.formSvc.initialize({
            saveApi: (payload) => this.mtrs.saveCLDocs('1', payload.items),
            // nếu có submitApi / approveApi thì thêm vào đây
        });
    }
    get items(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('items') as FormArrayCustom<FormGroup>;
    }
    private createRow(data?: Partial<CLDoc>, disable = false) {
        const id = uuid();
        return this.fb.group({
            id: [id],
            name: [{ value: data?.name || '', disabled: disable }, Validators.required],
            versionSelected: [data?.versionSelected || null],
            formExample: [data?.formExample || null],
            status: [data?.status || null],
            lastUpdate: [data?.lastUpdate || ''],
            updatedBy: [data?.updatedBy || ''],
        });
    }
    ngOnChanges(changes: SimpleChanges): void {}

    onChange(newVal: string) {
        this.changed.emit(newVal);
    }

    removeItem(rowId: number) {
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;

        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        this.items.removeAt(idx);
    }
    onClearFile(rowId: number) {
        const uploadId = this.uploadIds.get(rowId);
        if (uploadId) {
            this.fileUploadManagerService.cancel(uploadId);
            this.uploadIds.delete(rowId);
        }

        // Reset controls for this row
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx !== -1) {
            const ctrl = this.items.at(idx);
            ctrl.patchValue({ file: null, lastUpdate: null, updatedBy: '' });
        }
    }
    onFileSelected(files: any[], rowId: number) {
        const file = files[0];
        const fileName: string = files[0].name;
        if (!file) return;
        this.loadingRows.set(rowId, true);
        const uploadId = uuid();
        this.uploadIds.set(rowId, uploadId);

        const currentUser = this.authService.getPrinciple();
        // Find the row form group by id
        const idx = this.items.controls.findIndex((ctrl) => ctrl.get('id')?.value === rowId);
        if (idx === -1) return;
        const ctrl = this.items.at(idx);

        ctrl.patchValue({
            file,
            lastUpdate: this.datePipe.transform(new Date(), 'hh:mm dd/MM/yyyy'),
            updatedBy: currentUser?.email || '',
        });
        const sub = this.uploadService
            .analyzeFile(fileName, 'DOCUMENT')

            .pipe(
                tap((meta) => {
                    if (ctrl.get('filePath')) ctrl.get('filePath')?.setValue(meta.objectPath);
                }),
                switchMap((meta) => this.uploadService.uploadToPresignedUrl(files[0], meta.presignedUrl)),
                // dù complete hay error, luôn finish uploadId
                finalize(() => this.fileUploadManagerService.finish(uploadId)),
            )
            .subscribe({
                next: (p) => ({}),
                error: (e) => {
                    const list = this.otherDocsUploader.toArray();
                    if (list[idx]) list[idx].clearAll();
                },
                complete: () => {
                    this.loadingRows.set(rowId, false);
                },
            });
        this.subs.set(rowId, sub);
        // Hook cancel callback
        this.fileUploadManagerService.start(uploadId, () => {
            const s = this.subs.get(rowId);
            if (s) {
                s.unsubscribe();
                this.subs.delete(rowId);
            }
        });
    }

    /** Ghi đè API cụ thể cho tab này */
    protected saveApi(data: any) {
        return this.mtrs.saveCLDocs('1', data);
    }
    onSave() {
        if (this.formGroup.invalid) return;
        this.formSvc.save(this.formGroup.value).subscribe(() => {
            // hook thành công
            this.formGroup.resetForm();
        });
    }

    /** Kéo xử lý thành công thì emit và reset */
    // protected onSubmit(value) {
    //     console.log('value form submit', value);
    //     console.log('this.items.value', this.items.value);
    //     this.formGroup.resetForm();
    // }
    ngOnDestroy(): void {
        this.uploadSub.unsubscribe();
        console.log('🧹 [other-docs] Unmounted');
    }
}
