import { Component, inject, OnInit, <PERSON>, <PERSON><PERSON>hil<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Template<PERSON>ef, ViewChildren, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ProductInfoComponent } from 'src/app/modules/pms/components/product-info/product-info.component';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { DesignProfileComponent } from 'src/app/modules/pms/product-file/edit/components/design-profile/design-profile.component';
import { ProductionProfileComponent } from 'src/app/modules/pms/product-file/edit/components/production-profile/production-profile.component';
import { QualityProfileComponent } from 'src/app/modules/pms/product-file/edit/components/quality-profile/quality-profile.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { InputTextModule } from 'primeng/inputtext';
import { RouterLink } from '@angular/router';
import { StepsModule } from 'primeng/steps';
import { ITEMS_STEP, LIFECYCLE_STAGE_DOC_MAP, LIFECYCLE_STAGE_PRODUCT_MAP, STATUS_MAP } from 'src/app/models/constant/pms';
import { PanelModule } from 'primeng/panel';
import { ApprovalRequest, ProductDetail, ProductSoftware, ProductRecordVersion, DropdownOption, VersionRecord } from 'src/app/models/interface/pms';
import { BehaviorSubject, concatMap, finalize, from, map, Observable, of, skip, Subscription, switchMap, tap, toArray } from 'rxjs';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';

import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService } from 'primeng/api';

import { TableModule } from 'primeng/table';

import { EventBusService } from 'src/app/services/eventBus.service';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';
import { HttpResponse } from '@angular/common/http';
import { AlertService } from 'src/app/shared/services/alert.service';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ProductFileStateService } from '../../../../services/pms/product-file/product-file-state.service';
import { environment } from '../../../../../environments/environment';
import { FilterChangeEvent } from 'src/app/shared/table-module/custom-filter-table/custom-filter-table.component';
import { FileUploadManagerService } from 'src/app/services/upload/file-upload-manager.service';
import { ProcessFlowComponent } from '../components/process-flow/process-flow.component';

import { TabViewComponent } from 'src/app/shared/components/tab-view-dynamic/tab-view-dynamic.component';
import { RoutingComponent } from '../components/routing/routing.component';
import { ManbomComponent } from '../components/manbom/manbom.component';
import { BorComponent } from '../components/bor/bor.component';
import { SopApplicationComponent } from '../components/sop-application/sop-application.component';
import { OtherDocsComponent } from '../components/other-docs/other-docs.component';
import { CLDocsComponent } from '../components/CL-docs/CL-docs.component';
import { SopBaseComponent } from '../components/sop-base/sop-base.component';
import { MfgTechRecordService } from 'src/app/services/ptm/manufacturing-technology-records/mfg-tech-records.service';

interface Item {
    label: string;
    value: any;
}

@Component({
    selector: 'app-mfg-tech-records.edit',
    standalone: true,
    imports: [
        SubHeaderComponent,
        ProductInfoComponent,
        ButtonModule,
        DropdownModule,
        FormsModule,
        PopupComponent,
        FormCustomModule,
        HasAnyAuthorityDirective,
        CommonModule,
        InputTextModule,
        RouterLink,
        StepsModule,
        TabViewComponent,
        DesignProfileComponent,
        ProductionProfileComponent,
        QualityProfileComponent,
        WizardComponent,
        PanelModule,
        TableCommonModule,
        DialogModule,
        InputTextareaModule,
        TableModule,
        ProcessFlowComponent,
        RoutingComponent,
        ManbomComponent,
        BorComponent,
    ],
    templateUrl: './mfg-tech-records.edit.component.html',
    styleUrls: ['./mfg-tech-records.edit.component.scss'],
    providers: [ProductFileService],
})
export class MfgTechRecordsEditComponent implements OnInit, OnDestroy {
    //inject deps
    productFileStateService = inject(ProductFileStateService);
    private fb = inject(FormBuilder);

    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    //state
    isSaving: boolean = false;
    /** Cờ xác định đã gọi submit chưa (để guard 1 lần duy nhất) */
    private hasSubmitted = false;
    filterForm!: FormGroup;
    private productVersionOptions = new BehaviorSubject<DropdownOption[]>([]);
    public productVersionOptions$: Observable<DropdownOption[]> = this.productVersionOptions.asObservable();
    private stageOptions = new BehaviorSubject<DropdownOption[]>([]);
    public stageOptions$: Observable<DropdownOption[]> = this.stageOptions.asObservable();
    productSoftwareOptions: ProductSoftware[] = [];
    activeIndex: number = 0;
    itemsStep = ITEMS_STEP;
    currentProduct?: ProductDetail;
    version!: ProductRecordVersion;
    mode!: 'create' | 'view' | 'edit';
    selectedProduct: any;
    selectedPartNumber: any;
    selectedVersion: any;
    resultUrl: string | null = null;
    dataVersionClone: any[];
    records: VersionRecord[] = [];
    isNotClone: boolean = false;
    showStageFilter = true;
    shouldUseVersionRef: boolean = false;
    isSearching = false;
    isButtonVisible: boolean = false;
    isUploading = false;
    private uploadSub!: Subscription;
    versionRef: number;
    tabs = [];
    currentTab = 10;
    versionOptions: { label: string; value: any }[] = [];
    selectedLifecycleStage: Item = {
        label: 'Prototype',
        value: 1,
    };
    designMaterials: any = {
        productRecordSelected: null,
        listVersion: [],
    };
    stageMap = LIFECYCLE_STAGE_DOC_MAP;
    statusMap = STATUS_MAP;
    optionsLifecycleStage: Item[] = Object.entries(LIFECYCLE_STAGE_DOC_MAP).map(([key, label]) => ({
        value: Number(key),
        label: label,
    }));
    itemsHeader = [{ label: 'Quản lý hồ sơ CNSX' }, { label: 'Danh sách hồ sơ CNSX', url: '/ptm/manufacturing-technology-records' }, { label: 'Tạo mới' }];

    @ViewChild(TabViewComponent) tabView!: TabViewComponent;
    @ViewChild('processFlowTpl', { static: true })
    processFlowTpl!: TemplateRef<any>;

    @ViewChild('routingTpl', { static: true })
    routingTpl!: TemplateRef<any>;
    // Lấy tất cả instance của ProcessFlow + Routing đang render
    @ViewChildren(ProcessFlowComponent)
    private pfComponents!: QueryList<ProcessFlowComponent>;

    @ViewChildren(RoutingComponent)
    private rtComponents!: QueryList<RoutingComponent>;
    // … các ViewChild cho template tab khác …
    constructor(
        private route: ActivatedRoute,
        private bus: EventBusService,
        private router: Router,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private fileUploadManagerService: FileUploadManagerService,
        private mtrs: MfgTechRecordService,
        private productFileService: ProductFileService,
    ) {}

    ngOnDestroy() {
        this.uploadSub.unsubscribe();
    }
    handleProcessSubmit(val: any) {
        console.log('Submit từ ProcessFlow:', val);
    }

    handleRoutingSubmit(val: any) {
        console.log('Submit từ Routing:', val);
    }

    // loadVersionOptions(productId: number) {
    //     this.mtrs.getVersionOptions(this.currentProduct.id).subscribe((opts) => {
    //         console.log('opts', opts);
    //         this.versionOptions = opts.map((v) => {
    //             return {
    //                 label: v.versionName,
    //                 value: v.id,
    //             };
    //         });
    //     });
    // }
    buildTabs() {
        this.tabs = [
            {
                id: 'process',
                label: 'Process Flow',
                component: ProcessFlowComponent,
                inputs: {
                    title: 'Process Flow',
                    config: { type: 'warning' },
                    items: ['a', 'b', 'c'],
                    onSubmit: () => {
                        console.log('Submit clicked');
                    },
                    content: '',
                    editable: true,
                },
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'routing',
                label: 'Routing',
                component: RoutingComponent,
                inputs: {
                    content: 'Dữ liệu điều phối',
                    title: 'Routing',
                },
                outputs: {
                    submitted: (val: any) => this.handleRoutingSubmit(val),
                },
            },
            {
                id: 'pfmea',
                label: 'PFMEA',

                inputs: {
                    content: 'Dữ liệu điều phối',
                },
                outputs: {
                    submitted: (val: any) => this.handleRoutingSubmit(val),
                },
            },
            {
                id: 'work-standard',
                label: 'Work Standard',

                inputs: {
                    content: 'Dữ liệu kiểm tra',
                },
                outputs: {
                    submitted: (val: any) => this.handleRoutingSubmit(val),
                },
            },
            {
                id: 'tcc',
                label: 'TCC',

                inputs: {
                    content: 'Dữ liệu TCC',
                },
            },
            {
                id: 'manbom',
                label: 'MANBOM',
                component: ManbomComponent,
                inputs: {
                    title: 'MANBOM',
                    content: 'Dữ liệu MANBOM',
                },
            },
            {
                id: 'bor',
                label: 'BOR',
                component: BorComponent,
                inputs: {
                    title: 'BOR',
                    content: 'Dữ liệu BOR',
                },
            },
            {
                id: 'sop-base',
                label: 'SOP BASE',

                component: SopBaseComponent,
                inputs: {
                    product: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'sop-application',
                label: 'SOP Application',

                component: SopApplicationComponent,
                inputs: {
                    product: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'CL-docs',
                label: 'Bộ tài liệu CL',

                component: CLDocsComponent,
                inputs: {},
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'other-docs',
                label: 'Tài liệu khác',

                component: OtherDocsComponent,
                inputs: {},
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
        ];
    }
    ngOnInit() {
        const productId = +this.route.snapshot.paramMap.get('productId')!;
        const productRecordId = +this.route.snapshot.paramMap.get('productRecordId')!;
        this.getProductById(productId);
        this.loadListVersion(productId);
        this.getProductRecordById(productRecordId);
        this.uploadSub = this.fileUploadManagerService.isUploading$.subscribe((flag) => (this.isUploading = flag));

        this.route.url.subscribe((segments) => {
            // segments là 1 mảng UrlSegment,
            this.mode = segments[0].path as 'create' | 'edit' | 'view';
        });
        this.bus.on<void>('refreshTabs').subscribe(() => {
            this.refreshTabs();
        });
        const docObs$ = of(null);
        const navState = window.history.state || {};

        if (navState && navState.currentProduct) {
            this.currentProduct = navState.currentProduct;

            this.buildTabs();
        }
        if (navState.version && this.mode === 'edit') {
            this.version = navState.version;
            this.activeIndex = ITEMS_STEP.findIndex((item) => item.name === this.version.statusName);

            const productVersions = this.currentProduct.productVersions;
            const matched = productVersions.find((item) => item.id === this.version.id);
            if (matched) {
                const stage = matched.lifecycleStage;
                this.selectedLifecycleStage = {
                    label: LIFECYCLE_STAGE_DOC_MAP[stage],
                    value: stage,
                };
            } else {
                this.selectedLifecycleStage = {
                    label: 'Prototype',
                    value: 1,
                };
            }
        } else if (navState.version && this.mode === 'create') {
            //    Trường hợp nhân bản từ danh sách hồ sơ
            this.isNotClone = true;
            const payload = {
                selectedProduct: navState?.currentProduct,
                selectedVersion: navState?.version?.id,
                selectedStage: navState?.version?.lifecycleStage,
            };

            this.onSearch(payload, navState.option);
        } else if (navState.version && this.mode === 'view') {
            // const id = +this.route.snapshot.paramMap.get('versionId')!;

            this.version = navState.version as ProductRecordVersion;
            this.activeIndex = ITEMS_STEP.findIndex((item) => item.name === navState.version.statusName);
        }

        if (!navState.version && this.mode !== 'create') {
            // Trường hợp khi mở hồ sơ từ tab khác trạng thái edit
            const productId = +this.route.snapshot.paramMap.get('productId')!;
            const id = +this.route.snapshot.paramMap.get('versionId')!;
            this.version = { id: id } as ProductRecordVersion;
            this.productFileService.getDetailProduct(productId).subscribe({
                next: (res) => {
                    this.currentProduct = {
                        id: res.id,
                        lineId: res.lineId || 0,
                        lineName: res.lineName ? `${res.lineName}` : '',
                        name: res.name,
                        description: res.description ?? '',
                        vnptManPn: res.vnptManPn,
                        hardwareVersion: res.hardwareVersion ?? '',
                        firmwareVersion: res.firmwareVersion ?? '',
                        tradeName: res.tradeName,
                        tradeCode: res.tradeCode,
                        modelName: res.modelName ?? '',
                        generation: res.generation ?? '',
                        imageUrl: res.imageUrl ?? '',
                        imageName: res.imageName ?? '',
                        stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                        productVersions: res.productVersions,
                    };

                    if (res && res.productVersions?.length > 0) {
                        this.version = res.productVersions.filter((item) => {
                            if (item.id === id) {
                                return item;
                            }
                        })[0];
                        if (this.version) {
                            const statusCode = this.version.status;
                            const statusLabel = STATUS_MAP[statusCode];
                            const idx = ITEMS_STEP.findIndex((item) => item.name === statusLabel);
                            this.activeIndex = idx !== -1 ? idx : 0;
                            const stage = this.version.lifecycleStage;
                            this.selectedLifecycleStage = {
                                label: LIFECYCLE_STAGE_DOC_MAP[stage],
                                value: stage,
                            };
                        } else {
                            this.selectedLifecycleStage = {
                                label: 'Prototype',
                                value: 1,
                            };
                        }
                    }
                    this.buildTabs();
                },
                error: () => {},
            });
        }

        const lastLabel = this.mode === 'create' ? 'Tạo mới' : this.mode === 'edit' ? 'Chỉnh sửa' : 'Chi tiết';

        this.itemsHeader = [
            { label: 'Quản lý hồ sơ CNSX' },
            { label: 'Danh sách hồ sơ CNSX', url: '/ptm/manufacturing-technology-records' },
            { label: lastLabel },
        ];

        this.filterForm = this.fb.group({
            selectedProduct: [{ value: null, disabled: this.mode === 'view' }, Validators.required],
            selectedStage: [{ value: null, disabled: true }, Validators.required],
            selectedVersion: [{ value: null, disabled: true }, Validators.required],
            partNumber: [{ value: null, disabled: true }],
        });

        this.filterForm
            .get('selectedProduct')!
            .valueChanges.pipe(skip(1))
            .subscribe((line) => {
                this.showStageFilter = false;
                setTimeout(() => (this.showStageFilter = true), 0);
                const selectedStageValue = this.filterForm.get('selectedStage')!;
                selectedStageValue.reset(null, { emitEvent: false });
                this.filterForm.get('selectedVersion')!.reset();
                this.stageOptions.next([]);
                this.productVersionOptions.next([]);

                if (line) {
                    const uniqueStages = Array.from(new Set(line?.productVersions?.map((v) => v.lifecycleStage))).map((stage: number) => ({
                        value: stage,
                        label: LIFECYCLE_STAGE_DOC_MAP[stage],
                    }));

                    this.stageOptions.next(uniqueStages);
                    selectedStageValue.enable({ emitEvent: false });
                    this.filterForm.get('partNumber')!.patchValue(line?.vnptManPn || null, { emitEvent: false });
                } else {
                    this.filterForm.get('selectedVersion')!.reset();
                    this.filterForm.get('partNumber')!.patchValue(null, { emitEvent: false });
                    this.stageOptions.next([]);
                    selectedStageValue.reset();
                    selectedStageValue.disable({ emitEvent: false });
                }
            });
        this.filterForm
            .get('selectedStage')!
            .valueChanges.pipe(skip(1))
            .subscribe((stage) => {
                const nameCtrl = this.filterForm.get('selectedVersion')!;
                nameCtrl.reset(null, { emitEvent: false });
                const valueSelectedProduct = this.filterForm.get('selectedProduct')!.value;
                if (stage) {
                    const nextOptions = valueSelectedProduct?.productVersions.filter((v) => v.lifecycleStage === stage);
                    const mapNextOptions: DropdownOption[] = nextOptions.map((item) => {
                        return {
                            label: item.versionName,
                            value: item.id,
                        };
                    });

                    this.productVersionOptions.next(mapNextOptions);
                    nameCtrl.enable({ emitEvent: false });
                } else {
                    this.productVersionOptions.next([]);
                    nameCtrl.reset();
                    nameCtrl.disable({ emitEvent: false });
                }
            });
    }

    get configSelect() {
        // Chỉ gọi khi currentProduct đã khác null
        return {
            url: '/pr/api/product',
            usePost: false,
            rsql: false,
            page: 0,
            size: 1000,
            body: {
                unpaged: false,
                modelId: 0,
                customerId: 0,
                lineId: this.currentProduct!.lineId,
            },
            fieldLabel: 'name',
            dataKey: 'id',
        };
    }

    onTabChange(type: number) {
        console.log('onTabChange', type);
    }
    onFieldChanged(evt: { field: keyof ProductDetail; value: any; selected?: any }) {
        const { field, value, selected } = evt;
        console.log('selected', selected, 'field', field, 'value', value);
        if (!selected || !value) {
            this.designMaterials.productRecordSelected = null;
            return;
        }
        this.designMaterials.productRecordSelected = {
            ...selected,
            lifecycleStageText: this.stageMap[selected.lifecycleStage],
            statusText: this.statusMap[selected.status],
        };
        console.log('this.designMaterials.productRecordSelected', this.designMaterials.productRecordSelected);
    }
    loadListVersion(productId: number) {
        this.mtrs.getVersionOptions(productId).subscribe((opts) => {
            this.designMaterials.listVersion = opts.map((v) => {
                return {
                    label: v.versionName,
                    value: v.id,
                    ...v,
                };
            });
        });
    }
    getProductRecordById(ProductRecordId) {
        this.productFileService.getProductVersionById(ProductRecordId).subscribe({
            next: (res) => {
                console.log(res);
            },
            error: () => {},
        });
    }
    getProductById(productId) {
        this.productFileService.getDetailProduct(productId).subscribe({
            next: (res) => {
                this.currentProduct = {
                    id: res.id,
                    lineName: res.lineName ? `${res.lineName}` : '',
                    lineId: res.lineId || 0,
                    name: res.name,
                    description: res.description ?? '',
                    vnptManPn: res.vnptManPn,
                    hardwareVersion: res.hardwareVersion ?? '',
                    firmwareVersion: res.firmwareVersion ?? '',
                    tradeName: res.tradeName,
                    tradeCode: res.tradeCode,
                    modelName: res.modelName || '',
                    generation: res.generation || '',
                    imageUrl: res.imageUrl || '',
                    imageName: res.imageName || '',
                    stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                    productVersions: res.productVersions,
                };
            },
            error: () => {},
        });
    }
    onSearch(formValue: any, type: number): void {
        if (this.isSearching) {
            return;
        }
        this.isSearching = true;
        const state = window.history.state;
        //   TH nhân bản bên ngoài danh sách
        if (state && state?.version) {
            this.shouldUseVersionRef = state?.version && this.mode === 'create';
            this.versionRef = this.shouldUseVersionRef ? state.version.id : undefined;
        }
        //TH nhân bản bên trông màn thêm mới có cùng VNPT
        else {
            if (formValue.selectedProduct.id === this.currentProduct.id) {
                this.versionRef = formValue.selectedVersion;
            } else {
                this.versionRef = undefined;
            }
        }

        const versionId = formValue.selectedVersion;
        // 1 hồ sơ thiết kê , 2 hồ sơ sản xuất, 4 hồ sơ chất lượng , 7 cả ba
        const types = type;
    }

    onProductSelect(event: FilterChangeEvent) {
        const prod: ProductDetail = event.value;

        // Emit mảng mới cho dropdown
    }

    private refreshTabs() {}

    onSave(): void {
        // Cách 1: Gọi getData() cụ thể
        const processDataValidate = this.tabView.invokeMethod('process', 'validateProcess');
        let processData;
        if (processDataValidate) {
            processData = this.tabView.invokeMethod('process', 'getData');
        } else {
            this.tabView.invokeMethod('process', 'errorProcess');
        }
        const routingData = this.tabView.invokeMethod('routing', 'getData');
        console.log('Process:', processData);
        console.log('Routing:', routingData);

        // Cách 2: Gọi tất cả getData()
        const allData = this.tabView.getAllData();
        console.log('All:', allData);
    }

    handleClose() {
        this.router.navigate(['/pms/product-file'], {
            replaceUrl: true, // tránh tạo thêm history entry
        });
    }
    handleCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn Hủy',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.router.navigate(['/pms/product-file'], {
                    replaceUrl: true, // tránh tạo thêm history entry
                });
            },
        });
    }
    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }

    handleExport() {
        const versionId = this.version.id;
        this.productFileService.exportFileProdVer(versionId).subscribe(
            async (res: any) => {
                const blobText = await res.text();
                const url = `${this.STORAGE_BASE_URL}${blobText}`;
                const link = document.createElement('a');
                link.href = url;
                link.download = `product_version_${versionId}.xlsx`;
                link.click();
            },
            (error) => {
                console.error('Error downloading file:', error);
            },
        );
    }
}
