<app-sub-header [items]="itemsHeader" [action]="actionHeader">
    <ng-template #actionHeader>
        <p-button
            [disabled]="oldOrder.state === PO_STATE_CONSTANT.COMPLETED"
            size="small"
            label="Lưu"
            *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_po_edit']"
            severity="success"
            (click)="submitForm('formPO')"
        />
        <p-button label="Hủy" severity="secondary" routerLink="./.."></p-button>
    </ng-template>
</app-sub-header>
<div style="padding: 1rem 1rem 0 1rem">
    <app-form formId="formPO" [formGroup]="formGroup" layout="vertical" (onSubmit)="onSubmit($event)">
        <p-panel [toggleable]="true">
            <ng-template pTemplate="header">
                <div class="flex justify-between w-full tw-items-center">
                    <span class="tw-font-bold">Thông tin chung</span>
                    <span class="ml-auto tw-mr-5">
                        <app-wizard [currentState]="oldOrder.state" [states]="poStates" [canChangeState]="true" (stateChanged)="onChangeState($event)"></app-wizard>
                    </span>
                </div>
            </ng-template>

            <div class="tw-grid lg:tw-grid-cols-2 tw-grid-cols-1 tw-gap-4">
                <app-form-item label="Số đơn hàng (Order No)">
                    <input type="text" class="tw-w-full" pInputText formControlName="orderNo" />
                </app-form-item>

                <app-form-item label="Tra cứu trách nhiệm (Imputation)">
                    <input type="text" class="tw-w-full" pInputText formControlName="imputation" />
                </app-form-item>

                <app-form-item label="Ngày đặt hàng (Order date)">
                    <p-calendar
                        [showButtonBar]="true"
                        placeholder="dd/MM/yyyy"
                        [showIcon]="true"
                        dateFormat="dd/mm/yy"
                        class="tw-w-full"
                        formControlName="orderDateCustom"
                    ></p-calendar> </app-form-item
                ><app-form-item label="Số phiếu yêu cầu (PR No)">
                    <input type="text" class="tw-w-full" pInputText formControlName="requestNo" />
                </app-form-item>

                <app-form-item label="Tên nhà cung cấp">
                    <app-filter-table
                        type="select-one"
                        [configSelect]="{
                            fieldValue: 'id',
                            fieldLabel: 'name',
                            rsql: true,
                            url: '/sc/api/supplier/search',
                            paramForm: 'id',
                            body: {
                                state: [0, 2, 4, 5, 6],
                            },
                        }"
                        formControlName="supplierId"
                        (onChange)="handleChangeSupplier($event)"
                    ></app-filter-table>
                </app-form-item>

                <app-form-item label="Số nhà cung cấp (Supplier No)">
                    <input type="text" class="tw-w-full" pInputText formControlName="supplierNo" disabled="true" />
                </app-form-item>
            </div>
        </p-panel>
    </app-form>

    <br />
    <p-panel header="Dự toán chi phí" [toggleable]="true" *ngIf="!orderId">
        <app-po-boq [po]="oldOrder"></app-po-boq>
    </p-panel>

    <p-tabView *ngIf="orderId" [(activeIndex)]="activeIndex">
        <p-tabPanel header="Dự toán chi phí">
            <app-po-boq [po]="oldOrder"></app-po-boq>
        </p-tabPanel>
        <p-tabPanel header="Bảng kê">
            <ng-template pTemplate="content">
                <app-list-po-detail [po]="oldOrder"></app-list-po-detail>
            </ng-template>
        </p-tabPanel>
        <p-tabPanel header="Phiếu chuyển giao" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_transfer_import_view']">
            <ng-template pTemplate="content">
                <app-list-po-transfer (reloadPo)="reloadPo($event)" [po]="oldOrder" (updatePoDetails)="updatePoDetails($event)" [activeIndex]="activeIndex"></app-list-po-transfer>
            </ng-template>
        </p-tabPanel>
        <p-tabPanel header="Yêu cầu xuất kho" *appHasAnyAuthority="['ROLE_SYSTEM_ADMIN', 'sc_transfer_export_view']">
            <ng-template pTemplate="content">
                <app-list-po-transfer-export
                    [po]="oldOrder"
                    (updatePoDetails)="updatePoDetails($event)"
                    [activeIndex]="activeIndex"
                ></app-list-po-transfer-export>
            </ng-template>
        </p-tabPanel>
    </p-tabView>
</div>
