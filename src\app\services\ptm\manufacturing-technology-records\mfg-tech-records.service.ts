import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CLDoc, OtherDoc, UpdateOtherDocsPayload } from 'src/app/models/interface/ptm';

@Injectable({ providedIn: 'root' })
export class MfgTechRecordService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    // tab 1 process Flow
    // tab 10 Tài liệu khác
    saveCLDocs(instructionId: string, docs: CLDoc[]): Observable<OtherDoc[]> {
        const url = `${this.path}/CL/${instructionId}`;
        return this.#http.put<OtherDoc[]>(url, docs);
    }
    // tab 11 Tài liệu khác
    saveOtherDocs(productInstructionId: string, payload: UpdateOtherDocsPayload): Observable<OtherDoc[]> {
        const url = `${this.path}/${productInstructionId}/other-document`;
        return this.#http.post<OtherDoc[]>(url, payload);
    }
    getVersionOptions(productId: number): Observable<any[]> {
        const params = new HttpParams().set('productId', productId);
        return this.#http.get<any[]>('/pr/api/product-version', { params });
    }
}
