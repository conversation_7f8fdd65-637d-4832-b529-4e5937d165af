<div [ngClass]="class" [ngStyle]="style">
    <p-panel [header]="header" styleClass="product-info-panel" [toggleable]="true">
        <div *ngIf="hasProductInfo(); else noProduct" class="tw-grid tw-grid-cols-2 tw-gap-x-8">
            <!-- Cột trái -->
            <dl class="tw-grid tw-grid-cols-12 tw-gap-y-2">
                <ng-container *ngFor="let f of fieldsLeft">
                    <dt class="tw-col-span-4">{{ f.label }}</dt>
                    <dd class="tw-col-span-8">
                        <ng-container [ngSwitch]="f.type || 'text'">
                            <!-- Link -->
                            <ng-container *ngSwitchCase="'link'">
                                <a
                                    *ngIf="product?.[f.value!]"
                                    [href]="STORAGE_BASE_URL + '/' + product[f.url!]"
                                    target="_blank"
                                    class="tw-text-blue-600 tw-underline"
                                >
                                    {{ product[f.value!] }}
                                </a>
                            </ng-container>

                            <!-- Tag -->
                            <ng-container *ngSwitchCase="'tag'">
                                <p-tag *ngIf="product?.[f.value!]" [value]="product[f.value!]" severity="success"></p-tag>
                            </ng-container>

                            <!-- Select -->
                            <ng-container *ngSwitchCase="'select'">
                                <app-combobox-nonRSQL
                                    type="select-one"
                                    [options]="f.options"
                                    fieldValue="value"
                                    fieldLabel="label"
                                    [disabled]="!f.editable"
                                    placeholder="Chọn {{ f.label }}"
                                    (onChange)="handleSelected(f.value, $event)"
                                ></app-combobox-nonRSQL>
                            </ng-container>

                            <!-- Input Text -->
                            <ng-container *ngSwitchCase="'input'">
                                <input
                                    pInputText
                                    [(ngModel)]="product[f.value!]"
                                    [readonly]="!f.editable"
                                    [placeholder]="f.placeholder"
                                    class="p-inputtext-sm w-full"
                                />
                            </ng-container>

                            <!-- Default = Text -->
                            <ng-container *ngSwitchDefault>
                                {{ product?.[f.value!] ?? '' }}
                            </ng-container>
                        </ng-container>
                    </dd>
                </ng-container>
            </dl>

            <!-- Cột phải -->
            <dl class="tw-grid tw-grid-cols-12 tw-gap-y-2">
                <ng-container *ngFor="let f of fieldsRight">
                    <dt class="tw-col-span-4">{{ f.label }}</dt>
                    <dd class="tw-col-span-8">
                        <ng-container [ngSwitch]="f.type || 'text'">
                            <!-- Link -->
                            <ng-container *ngSwitchCase="'link'">
                                <a
                                    *ngIf="product?.[f.value!]"
                                    [href]="STORAGE_BASE_URL + '/' + product[f.url!]"
                                    target="_blank"
                                    class="tw-text-blue-600 tw-underline"
                                >
                                    {{ product[f.value!] }}
                                </a>
                            </ng-container>

                            <!-- Tag -->
                            <ng-container *ngSwitchCase="'tag'">
                                <p-tag *ngIf="product?.[f.value!]" [value]="product[f.value!]" severity="success"></p-tag>
                            </ng-container>

                            <!-- Select -->
                            <ng-container *ngSwitchCase="'select'">
                                <app-combobox-nonRSQL
                                    type="select-one"
                                    [options]="f.options"
                                    fieldValue="value"
                                    fieldLabel="label"
                                    [disabled]="!f.editable"
                                    placeholder="Chọn {{ f.label }}"
                                    (onChange)="handleSelected(f.value, $event)"
                                ></app-combobox-nonRSQL>
                            </ng-container>

                            <!-- Input Text -->
                            <ng-container *ngSwitchCase="'input'">
                                <input
                                    pInputText
                                    [(ngModel)]="product[f.value!]"
                                    [readonly]="!f.editable"
                                    [placeholder]="f.placeholder"
                                    class="p-inputtext-sm w-full"
                                />
                            </ng-container>

                            <!-- Default = Text -->
                            <ng-container *ngSwitchDefault>
                                {{ product?.[f.value!] ?? '' }}
                            </ng-container>
                        </ng-container>
                    </dd>
                </ng-container>
            </dl>
        </div>

        <ng-template #noProduct>
            <p class="tw-text-center tw-text-gray-500 tw-italic">{{ emptyMessage }}</p>
        </ng-template>
    </p-panel>
</div>
