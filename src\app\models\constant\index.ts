export const listContractStatus = [
    {
        value: 0,
        lable: '<PERSON><PERSON><PERSON> thực hiện',
    },
    {
        value: 1,
        lable: '<PERSON>ang thi công',
    },
    {
        value: 2,
        lable: '<PERSON>àn thành',
    },
];

export const listApproveStatus = [
    {
        value: 0,
        lable: 'Chờ duyệt',
    },
    {
        value: 1,
        lable: 'Từ chối',
    },
    {
        value: 2,
        lable: 'Chờ PM duyệt',
    },
    {
        value: 3,
        lable: 'PM Từ chối',
    },
    {
        value: 4,
        lable: 'Hoàn thành',
    },
];

export const listApproveStatusPM = [
    {
        value: 2,
        lable: 'Chờ PM duyệt',
    },
    {
        value: 3,
        lable: 'PM Từ chối',
    },
    {
        value: 4,
        lable: 'Hoàn thành',
    },
];

// mỗi màn sẽ có 1 key duy nhất, ko đc trùng lặp giữa các màn. Nếu muốn lấy cache thì có thể trùng
export const TABLE_KEY = {
    CATEGORY: 'CATEGORY',
    AREA: 'AREA',
    DISTRICT: 'DISTRICT',
    ROLE: 'ROLE',
    PRIVILEGE: 'PRIVILEGE',
    PROCESS: 'PROCESS',
    STATE: 'STATE',
    USER: 'USER',
    APPROVE: ' APPROVE',
    CONTRACT: 'CONTRACT',
    TEMPLATE: 'TEMPLATE',
    NOTIFICATION: 'NOTIFICATION',
    ACCEPTANCE_DOCUMENT: 'ACCEPTANCE_DOCUMENT',
    PO: 'PO',
    SUPPLIER_INFOR: 'SUPPLIER_INFOR',
    SUPPLIER_INFOR_DELETE: 'SUPPLIER_INFOR_DELETE',
    SUPPLIER_TYPE: 'SUPPLIER_TYPE',
    SUPPLIER_EVALUATE: 'SUPPLIER_EVALUATE',
    SCC_PO: 'SCC_PO',
    MANTENANCE: 'MANTENANCE',
    LOGISTICS_FORWARDER: 'LOGISTICS_FORWARDER',
    LOGISTICS_EXPRESS_DELIVERY: 'LOGISTICS_EXPRESS_DELIVERY',
    LOGISTICS_INSURANCE: 'LOGISTICS_INSURANCE',
    LOGISTICS_OTHER: 'LOGISTICS_OTHER',
    DOMESTIC_PRICE: 'DOMESTIC_PRICE',
    BO: 'BO',
    BO_TRACKING_DOING: 'BO_TRACKING_DOING',
    BO_TRACKING_SUCCESS: 'BO_TRACKING_SUCCESS',
    MONTHLY_EXPENSES_FORWARDER: 'MONTHLY_EXPENSES_FORWARDER',
    MONTHLY_EXPENSES_INSURANCE: 'MONTHLY_EXPENSES_INSURANCE',
    MONTHLY_EXPENSES_EXPRESS_DELIVERY: 'MONTHLY_EXPENSES_EXPRESS_DELIVERY',
    PRICE_ASK_LIST: 'PRICE_ASK_LIST',
    BOM_TABLE: 'BOM_TABLE',
    PO_DRAFT_TABLE: 'PO_DRAFT_TABLE',
    MATERIAL_SUPPLIER_STATE_TWO: 'MATERIAL_SUPPLIER_STATE_TWO',
    REQUEST_SHIPPING: 'REQUEST_SHIPPING',
    InformationRetrieval: 'InformationRetrieval',
    SummaryReport: 'SummaryReport',
    SOFTWARE_RESOURCE: 'software_resource',
};

export const MODULE = {
    BOS: {
        name: 'BOS',
        title: 'BOS',
    },
    SMART_QC: {
        name: 'SMART_QC',
        title: 'SmartQC',
    },
    ADMINISTRATION: {
        name: 'ADMINISTRATION',
        title: 'Quản trị hệ thống',
    },
    SUPPLIER_CHAIN: {
        name: 'SUPPLIER_CHAIN',
        title: 'Quản lý cung ứng',
    },
    // Vivas ---------------
    PMS: {
        name: 'PMS',
        title: 'Quản lý hồ sơ sản phẩm',
    },
    PTM: {
        name: 'PTM',
        title: 'Quản lý công nghệ sản xuất',
    },
    // Vivas ---------------
};

export interface Module {
    name: string;
    title: string;
}

export const TIME_TYPE_SUPPLIER_KPI = {
    YEAR: 0,
    MONTH: 1,
};

export const CheckListType = {
    IMAGE: 1, // hình ảnh
    TEXT: 2, // văn bản
    OPTION: 3, // lựa chọn
    NUMBER: 5, // số âm/dương
    ANGLE: 6, // góc độ
    YES_NO: 7, // yes/no
    BARCODE: 8, // barcode
    QRCODE: 9, // QR code
};

export const MIN_TIMESTAMP_DATE = -8640000000000000;
export const MAX_TIMESTAMP_DATE = 8640000000000000;
